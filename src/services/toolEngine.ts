import {
  Tool<PERSON>onfig,
  Too<PERSON><PERSON><PERSON><PERSON>ionContext,
  ToolE<PERSON>,
  ParameterInjectionContext,
  ToolTestResult,
  ToolExecutionEvent,
} from "@/types/api";
import { wsService } from "./websocket";
import { stateManager } from "./state";
import { z } from "zod";

// Production-grade constants
const EXECUTION_CONSTANTS = {
  MAX_CONCURRENT_EXECUTIONS: 100,
  DEFAULT_TIMEOUT: 30000,
  MAX_TIMEOUT: 300000,
  MIN_TIMEOUT: 1000,
  DEFAULT_RETRY_ATTEMPTS: 3,
  MAX_RETRY_ATTEMPTS: 10,
  RETRY_BACKOFF_BASE: 2,
  CACHE_TTL_DEFAULT: 300000, // 5 minutes
  RATE_LIMIT_WINDOW: 60000, // 1 minute
  MEMORY_LIMIT_MB: 128,
  CPU_LIMIT_PERCENT: 50,
  MAX_INPUT_SIZE_BYTES: 1024 * 1024, // 1MB
  MAX_OUTPUT_SIZE_BYTES: 10 * 1024 * 1024, // 10MB
} as const;

// Enhanced error types
class ToolExecutionError extends Error {
  constructor(
    message: string,
    public code: string,
    public category:
      | "validation"
      | "execution"
      | "timeout"
      | "rate_limit"
      | "resource"
      | "network"
      | "auth",
    public retryable: boolean = false,
    public details?: Record<string, any>,
  ) {
    super(message);
    this.name = "ToolExecutionError";
  }
}

// Rate limiter implementation
class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(toolId: string, limit: number, windowMs: number): boolean {
    const now = Date.now();
    const requests = this.requests.get(toolId) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter((time) => now - time < windowMs);

    if (validRequests.length >= limit) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(toolId, validRequests);
    return true;
  }

  getRemainingRequests(
    toolId: string,
    limit: number,
    windowMs: number,
  ): number {
    const now = Date.now();
    const requests = this.requests.get(toolId) || [];
    const validRequests = requests.filter((time) => now - time < windowMs);
    return Math.max(0, limit - validRequests.length);
  }
}

// Cache implementation
class ToolCache {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> =
    new Map();

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  set(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  generateKey(toolId: string, input: any, strategy: string): string {
    if (strategy === "input_hash") {
      return `${toolId}:${this.hashObject(input)}`;
    }
    return `${toolId}:${Date.now()}`;
  }

  private hashObject(obj: any): string {
    return btoa(JSON.stringify(obj))
      .replace(/[^a-zA-Z0-9]/g, "")
      .substring(0, 32);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

class ToolEngine {
  private executionContexts: Map<string, ToolExecutionContext> = new Map();
  private analytics: Map<string, any> = new Map();
  private eventHandlers: Map<string, ((event: ToolEvent) => void)[]> =
    new Map();
  private rateLimiter: RateLimiter = new RateLimiter();
  private cache: ToolCache = new ToolCache();
  private activeExecutions: Set<string> = new Set();
  private executionQueue: Array<{
    context: ToolExecutionContext;
    resolve: Function;
    reject: Function;
  }> = [];
  private isProcessingQueue: boolean = false;

  constructor() {
    this.setupEventListeners();
    this.startQueueProcessor();
    this.startPeriodicCleanup();
  }

  private setupEventListeners() {
    wsService.on("tool:execution_update", (data) => {
      this.handleExecutionUpdate(data);
    });

    wsService.on("tool:analytics_update", (data) => {
      this.handleAnalyticsUpdate(data);
    });
  }

  private handleExecutionUpdate(data: {
    contextId: string;
    update: Partial<ToolExecutionContext>;
  }) {
    const context = this.executionContexts.get(data.contextId);
    if (context) {
      Object.assign(context, data.update);
      this.emitEvent({
        id: this.generateEventId(),
        type: "tool_call_result",
        toolId: context.toolId,
        sessionId: context.sessionId,
        timestamp: new Date(),
        data: data.update,
      });
    }
  }

  private handleAnalyticsUpdate(data: { toolId: string; analytics: any }) {
    this.analytics.set(data.toolId, data.analytics);
  }

  public async executeToolWithContext(
    toolConfig: ToolConfig,
    input: any,
    sessionId: string,
    injectionContext?: ParameterInjectionContext,
  ): Promise<ToolExecutionContext> {
    // Validate input size
    const inputSize = JSON.stringify(input).length;
    if (inputSize > EXECUTION_CONSTANTS.MAX_INPUT_SIZE_BYTES) {
      throw new ToolExecutionError(
        `Input size ${inputSize} bytes exceeds maximum ${EXECUTION_CONSTANTS.MAX_INPUT_SIZE_BYTES} bytes`,
        "INPUT_TOO_LARGE",
        "validation",
      );
    }

    // Check concurrent execution limits
    if (
      this.activeExecutions.size >=
      EXECUTION_CONSTANTS.MAX_CONCURRENT_EXECUTIONS
    ) {
      throw new ToolExecutionError(
        "Maximum concurrent executions reached",
        "CONCURRENT_LIMIT_EXCEEDED",
        "resource",
      );
    }

    // Check rate limits
    if (toolConfig.rateLimit?.enabled) {
      const allowed = this.rateLimiter.isAllowed(
        toolConfig.id || "temp",
        toolConfig.rateLimit.requestsPerMinute,
        EXECUTION_CONSTANTS.RATE_LIMIT_WINDOW,
      );
      if (!allowed) {
        throw new ToolExecutionError(
          "Rate limit exceeded",
          "RATE_LIMIT_EXCEEDED",
          "rate_limit",
          true,
        );
      }
    }

    const contextId = this.generateContextId();
    const correlationId = this.generateCorrelationId();

    const executionContext: ToolExecutionContext = {
      id: contextId,
      toolId: toolConfig.id || "temp",
      sessionId,
      correlationId,
      startTime: new Date(),
      status: "pending",
      input,
      executionContext: {
        isolated: toolConfig.execution?.isolated ?? true,
        resourceLimits: {
          maxMemoryMB:
            toolConfig.execution?.resourceLimits?.maxMemoryMB ??
            EXECUTION_CONSTANTS.MEMORY_LIMIT_MB,
          maxCpuPercent:
            toolConfig.execution?.resourceLimits?.maxCpuPercent ??
            EXECUTION_CONSTANTS.CPU_LIMIT_PERCENT,
          maxExecutionTimeMs:
            toolConfig.execution?.resourceLimits?.maxExecutionTimeMs ??
            toolConfig.timeout,
        },
        environment: toolConfig.execution?.environment,
      },
      metadata: {
        retryCount: 0,
        networkCalls: 0,
        validationErrors: [],
      },
      events: [],
    };

    this.executionContexts.set(contextId, executionContext);
    this.activeExecutions.add(contextId);

    try {
      // Add execution event
      this.addExecutionEvent(
        executionContext,
        "execution_started",
        "Tool execution initiated",
      );

      // Emit start event
      this.emitEvent({
        id: this.generateEventId(),
        type: "tool_call_start",
        toolId: executionContext.toolId,
        sessionId,
        executionId: contextId,
        correlationId,
        timestamp: new Date(),
        data: { input, context: injectionContext },
        severity: "info",
        source: "engine",
      });

      // Check cache first
      let cacheKey: string | null = null;
      if (toolConfig.caching?.enabled) {
        cacheKey = this.cache.generateKey(
          toolConfig.id || "temp",
          input,
          toolConfig.caching.keyStrategy || "input_hash",
        );
        const cachedResult = this.cache.get(cacheKey);
        if (cachedResult) {
          executionContext.status = "completed";
          executionContext.output = cachedResult;
          executionContext.endTime = new Date();
          executionContext.metadata.cacheHit = true;
          executionContext.metadata.executionTime =
            Date.now() - executionContext.startTime.getTime();

          this.addExecutionEvent(
            executionContext,
            "cache_hit",
            "Result retrieved from cache",
          );
          this.emitEvent({
            id: this.generateEventId(),
            type: "tool_cache_hit",
            toolId: executionContext.toolId,
            sessionId,
            executionId: contextId,
            correlationId,
            timestamp: new Date(),
            data: { output: cachedResult },
            severity: "info",
            source: "cache",
          });

          return executionContext;
        }
      }

      // Update status to running
      executionContext.status = "running";
      this.addExecutionEvent(
        executionContext,
        "status_changed",
        "Status changed to running",
      );

      // Inject parameters if configured
      const processedInput = await this.injectParameters(
        toolConfig,
        input,
        injectionContext,
      );
      executionContext.processedInput = processedInput;
      this.addExecutionEvent(
        executionContext,
        "parameters_injected",
        "Input parameters processed and injected",
      );

      // Validate input against schema
      const validationResult = await this.validateInput(
        toolConfig,
        processedInput,
      );
      if (!validationResult.valid) {
        executionContext.metadata.validationErrors =
          validationResult.errors || [];
        this.addExecutionEvent(
          executionContext,
          "validation_failed",
          `Input validation failed: ${validationResult.errors?.join(", ")}`,
        );

        this.emitEvent({
          id: this.generateEventId(),
          type: "tool_validation_failed",
          toolId: executionContext.toolId,
          sessionId,
          executionId: contextId,
          correlationId,
          timestamp: new Date(),
          data: { errors: validationResult.errors },
          severity: "error",
          source: "validator",
        });

        throw new ToolExecutionError(
          `Input validation failed: ${validationResult.errors?.join(", ")}`,
          "VALIDATION_FAILED",
          "validation",
          false,
          { errors: validationResult.errors },
        );
      }
      this.addExecutionEvent(
        executionContext,
        "validation_passed",
        "Input validation successful",
      );

      // Execute the tool with timeout and retry logic
      const result = await this.executeWithRetryAndTimeout(
        toolConfig,
        processedInput,
        executionContext,
      );

      // Validate output size
      const outputSize = JSON.stringify(result).length;
      if (outputSize > EXECUTION_CONSTANTS.MAX_OUTPUT_SIZE_BYTES) {
        throw new ToolExecutionError(
          `Output size ${outputSize} bytes exceeds maximum ${EXECUTION_CONSTANTS.MAX_OUTPUT_SIZE_BYTES} bytes`,
          "OUTPUT_TOO_LARGE",
          "validation",
        );
      }

      // Validate output against schema
      const outputValidation = await this.validateOutput(toolConfig, result);
      if (!outputValidation.valid) {
        this.addExecutionEvent(
          executionContext,
          "output_validation_warning",
          `Output validation failed: ${outputValidation.errors?.join(", ")}`,
        );
        console.warn(
          `Output validation failed: ${outputValidation.errors?.join(", ")}`,
        );
      } else {
        this.addExecutionEvent(
          executionContext,
          "output_validation_passed",
          "Output validation successful",
        );
      }

      // Update execution context
      executionContext.status = "completed";
      executionContext.output = result;
      executionContext.endTime = new Date();
      executionContext.metadata.executionTime =
        executionContext.endTime.getTime() -
        executionContext.startTime.getTime();

      this.addExecutionEvent(
        executionContext,
        "execution_completed",
        `Tool executed successfully in ${executionContext.metadata.executionTime}ms`,
      );

      // Cache the result if caching is enabled
      if (toolConfig.caching?.enabled && cacheKey) {
        this.cache.set(
          cacheKey,
          result,
          toolConfig.caching.ttl || EXECUTION_CONSTANTS.CACHE_TTL_DEFAULT,
        );
        this.addExecutionEvent(
          executionContext,
          "result_cached",
          "Result stored in cache",
        );
      }

      // Emit success event
      this.emitEvent({
        id: this.generateEventId(),
        type: "tool_call_result",
        toolId: executionContext.toolId,
        sessionId,
        executionId: contextId,
        correlationId,
        timestamp: new Date(),
        data: {
          output: result,
          executionTime: executionContext.metadata.executionTime,
          cacheHit: executionContext.metadata.cacheHit || false,
          retryCount: executionContext.metadata.retryCount,
        },
        severity: "info",
        source: "engine",
      });

      // Update analytics
      await this.updateAnalytics(
        toolConfig.id!,
        true,
        executionContext.metadata.executionTime!,
      );

      return executionContext;
    } catch (error: any) {
      // Handle execution error
      executionContext.status =
        error instanceof ToolExecutionError &&
        error.code === "RATE_LIMIT_EXCEEDED"
          ? "rate_limited"
          : "failed";
      executionContext.error = error.message;
      executionContext.errorCode = error.code || "UNKNOWN_ERROR";
      executionContext.stackTrace = error.stack;
      executionContext.endTime = new Date();
      executionContext.metadata.executionTime =
        executionContext.endTime.getTime() -
        executionContext.startTime.getTime();

      this.addExecutionEvent(
        executionContext,
        "execution_failed",
        `Tool execution failed: ${error.message}`,
      );

      // Determine if error is retryable
      const isRetryable =
        error instanceof ToolExecutionError ? error.retryable : true;

      // Try fallback strategy if configured
      if (
        toolConfig.hitl?.fallbackStrategy &&
        toolConfig.hitl?.fallbackStrategy !== "none" &&
        isRetryable
      ) {
        try {
          this.addExecutionEvent(
            executionContext,
            "fallback_triggered",
            `Attempting fallback strategy: ${toolConfig.hitl.fallbackStrategy}`,
          );

          this.emitEvent({
            id: this.generateEventId(),
            type: "tool_fallback_triggered",
            toolId: executionContext.toolId,
            sessionId,
            executionId: contextId,
            correlationId,
            timestamp: new Date(),
            data: {
              strategy: toolConfig.hitl.fallbackStrategy,
              originalError: error.message,
            },
            severity: "warning",
            source: "engine",
          });

          const fallbackResult = await this.executeFallback(
            toolConfig,
            input,
            error,
            executionContext,
          );

          executionContext.output = fallbackResult;
          executionContext.status = "completed";
          executionContext.metadata.fallbackUsed = true;

          this.addExecutionEvent(
            executionContext,
            "fallback_succeeded",
            "Fallback strategy executed successfully",
          );

          this.emitEvent({
            id: this.generateEventId(),
            type: "tool_call_result",
            toolId: executionContext.toolId,
            sessionId,
            executionId: contextId,
            correlationId,
            timestamp: new Date(),
            data: { output: fallbackResult, fallbackUsed: true },
            severity: "info",
            source: "engine",
          });

          await this.updateAnalytics(
            toolConfig.id!,
            true,
            executionContext.metadata.executionTime!,
          );
          return executionContext;
        } catch (fallbackError: any) {
          this.addExecutionEvent(
            executionContext,
            "fallback_failed",
            `Fallback strategy failed: ${fallbackError.message}`,
          );
          console.error("Fallback execution failed:", fallbackError);
        }
      }

      // Emit error event
      const eventType =
        executionContext.status === "rate_limited"
          ? "tool_call_rate_limited"
          : "tool_call_error";
      this.emitEvent({
        id: this.generateEventId(),
        type: eventType as any,
        toolId: executionContext.toolId,
        sessionId,
        executionId: contextId,
        correlationId,
        timestamp: new Date(),
        data: {
          error: error.message,
          errorCode: executionContext.errorCode,
          executionTime: executionContext.metadata.executionTime,
          retryCount: executionContext.metadata.retryCount,
          category:
            error instanceof ToolExecutionError ? error.category : "execution",
        },
        severity:
          executionContext.status === "rate_limited" ? "warning" : "error",
        source: "engine",
      });

      // Update analytics
      await this.updateAnalytics(
        toolConfig.id!,
        false,
        executionContext.metadata.executionTime!,
      );

      return executionContext;
    } finally {
      // Always clean up
      this.activeExecutions.delete(contextId);

      // Update analytics regardless of success/failure
      if (toolConfig.monitoring?.enabled !== false) {
        await this.updateAnalytics(
          toolConfig.id!,
          executionContext.status === "completed",
          executionContext.metadata.executionTime || 0,
          executionContext,
        );
      }
    }
  }

  private async injectParameters(
    toolConfig: ToolConfig,
    input: any,
    injectionContext?: ParameterInjectionContext,
  ): Promise<any> {
    if (!toolConfig.hitl?.parameterBinding || !injectionContext) {
      return input;
    }

    const processedInput = { ...input };

    // Inject session context
    if (
      toolConfig.hitl?.parameterBinding.sessionContext &&
      injectionContext.sessionContext
    ) {
      Object.assign(processedInput, injectionContext.sessionContext);
    }

    // Inject user context
    if (
      toolConfig.hitl?.parameterBinding.userContext &&
      injectionContext.userContext
    ) {
      Object.assign(processedInput, injectionContext.userContext);
    }

    // Inject agent memory
    if (
      toolConfig.hitl?.parameterBinding.agentMemory &&
      injectionContext.agentMemory
    ) {
      processedInput.memory = injectionContext.agentMemory;
    }

    // Inject custom bindings
    if (
      toolConfig.hitl?.parameterBinding.customBindings &&
      injectionContext.customBindings
    ) {
      Object.entries(toolConfig.hitl?.parameterBinding.customBindings).forEach(
        ([key, value]) => {
          if (injectionContext.customBindings![value]) {
            processedInput[key] = injectionContext.customBindings![value];
          }
        },
      );
    }

    return processedInput;
  }

  private async validateInput(
    toolConfig: ToolConfig,
    input: any,
  ): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const schema = JSON.parse(toolConfig.inputSchema);
      // In a real implementation, you would use a JSON schema validator like Ajv
      // For now, we'll do basic validation
      return { valid: true };
    } catch (error) {
      return { valid: false, errors: ["Invalid input schema"] };
    }
  }

  private async validateOutput(
    toolConfig: ToolConfig,
    output: any,
  ): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const schema = JSON.parse(toolConfig.outputSchema);
      // In a real implementation, you would use a JSON schema validator like Ajv
      // For now, we'll do basic validation
      return { valid: true };
    } catch (error) {
      return { valid: false, errors: ["Invalid output schema"] };
    }
  }

  private async executeWithRetryAndTimeout(
    toolConfig: ToolConfig,
    input: any,
    executionContext: ToolExecutionContext,
  ): Promise<any> {
    const maxRetries = toolConfig.retryAttempts || 3;
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        executionContext.metadata.retryCount = attempt;

        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(
              new Error(`Tool execution timeout after ${toolConfig.timeout}ms`),
            );
          }, toolConfig.timeout);
        });

        // Create execution promise
        const executionPromise = this.executeHttpCall(toolConfig, input);

        // Race between execution and timeout
        const result = await Promise.race([executionPromise, timeoutPromise]);
        return result;
      } catch (error: any) {
        lastError = error;

        if (error.message.includes("timeout")) {
          executionContext.status = "timeout";
          this.emitEvent({
            id: this.generateEventId(),
            type: "tool_call_timeout",
            toolId: executionContext.toolId,
            sessionId: executionContext.sessionId,
            timestamp: new Date(),
            data: { attempt, timeout: toolConfig.timeout },
          });
        }

        // If this is the last attempt, throw the error
        if (attempt === maxRetries) {
          throw lastError;
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) =>
          setTimeout(resolve, Math.pow(2, attempt) * 1000),
        );
      }
    }

    throw lastError!;
  }

  private async executeHttpCall(
    toolConfig: ToolConfig,
    input: any,
  ): Promise<any> {
    if (!toolConfig.endpoint) {
      throw new Error("No endpoint configured for tool");
    }

    const headers = {
      "Content-Type": "application/json",
      ...toolConfig.headers,
    };

    const config: RequestInit = {
      method: toolConfig.method,
      headers,
    };

    // Add body for POST, PUT methods
    if (["POST", "PUT"].includes(toolConfig.method)) {
      config.body = JSON.stringify(input);
    }

    // For GET requests, append query parameters
    let url = toolConfig.endpoint;
    if (toolConfig.method === "GET" && input && typeof input === "object") {
      const params = new URLSearchParams();
      Object.entries(input).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
    }

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      return await response.json();
    } else {
      return await response.text();
    }
  }

  private async executeFallback(
    toolConfig: ToolConfig,
    input: any,
    originalError: Error,
    executionContext: ToolExecutionContext,
  ): Promise<any> {
    switch (toolConfig.hitl?.fallbackStrategy) {
      case "retry":
        return await this.executeWithRetryAndTimeout(
          toolConfig,
          input,
          executionContext,
        );
      case "none":
        throw originalError;
      case "timeout":
        return await this.executeWithRetryAndTimeout(
          toolConfig,
          input,
          executionContext,
        );

      default:
        throw originalError;
    }
  }

  private async updateAnalytics(
    toolId: string,
    success: boolean,
    executionTime: number,
    context?: ToolExecutionContext,
  ) {
    const current = this.analytics.get(toolId) || {
      toolId,
      timeRange: { start: new Date(), end: new Date() },
      usage: {
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        cancelledCalls: 0,
        timeoutCalls: 0,
        rateLimitedCalls: 0,
      },
      performance: {
        averageLatency: 0,
        medianLatency: 0,
        p50Latency: 0,
        p90Latency: 0,
        p95Latency: 0,
        p99Latency: 0,
        minLatency: Infinity,
        maxLatency: 0,
        throughput: 0,
        concurrentExecutions: 0,
      },
      reliability: {
        errorRate: 0,
        timeoutRate: 0,
        successRate: 0,
        availabilityPercent: 100,
        mttr: 0,
        mtbf: 0,
      },
      resources: {
        averageMemoryUsage: 0,
        peakMemoryUsage: 0,
        averageCpuUsage: 0,
        peakCpuUsage: 0,
        networkBytesIn: 0,
        networkBytesOut: 0,
      },
      errors: {
        byType: {},
        byCode: {},
        topErrors: [],
      },
      caching: {
        hitRate: 0,
        missRate: 0,
        totalHits: 0,
        totalMisses: 0,
        averageHitLatency: 0,
        averageMissLatency: 0,
      },
      rateLimit: {
        totalLimited: 0,
        limitRate: 0,
        averageWaitTime: 0,
      },
      hitl: {
        totalRequests: 0,
        resolvedRequests: 0,
        timeoutRequests: 0,
        averageResolutionTime: 0,
      },
      trends: {
        usageGrowth: 0,
        performanceTrend: "stable" as const,
        errorTrend: "stable" as const,
      },
      lastUpdated: new Date(),
      latencyHistory: [] as number[],
    };

    // Update usage statistics
    current.usage.totalCalls++;
    current.timeRange.end = new Date();
    current.lastUpdated = new Date();

    if (success) {
      current.usage.successfulCalls++;
    } else if (context?.status === "timeout") {
      current.usage.timeoutCalls++;
    } else if (context?.status === "rate_limited") {
      current.usage.rateLimitedCalls++;
      current.rateLimit.totalLimited++;
    } else {
      current.usage.failedCalls++;
    }

    // Update performance metrics
    if (executionTime > 0) {
      current.latencyHistory = current.latencyHistory || [];
      current.latencyHistory.push(executionTime);

      // Keep only last 1000 measurements for percentile calculations
      if (current.latencyHistory.length > 1000) {
        current.latencyHistory = current.latencyHistory.slice(-1000);
      }

      const sortedLatencies = [...current.latencyHistory].sort((a, b) => a - b);
      const len = sortedLatencies.length;

      current.performance.averageLatency =
        current.latencyHistory.reduce((a, b) => a + b, 0) / len;
      current.performance.medianLatency = sortedLatencies[Math.floor(len / 2)];
      current.performance.p50Latency = sortedLatencies[Math.floor(len * 0.5)];
      current.performance.p90Latency = sortedLatencies[Math.floor(len * 0.9)];
      current.performance.p95Latency = sortedLatencies[Math.floor(len * 0.95)];
      current.performance.p99Latency = sortedLatencies[Math.floor(len * 0.99)];
      current.performance.minLatency = Math.min(
        current.performance.minLatency,
        executionTime,
      );
      current.performance.maxLatency = Math.max(
        current.performance.maxLatency,
        executionTime,
      );
    }

    // Update reliability metrics
    current.reliability.successRate =
      current.usage.successfulCalls / current.usage.totalCalls;
    current.reliability.errorRate =
      current.usage.failedCalls / current.usage.totalCalls;
    current.reliability.timeoutRate =
      current.usage.timeoutCalls / current.usage.totalCalls;
    current.rateLimit.limitRate =
      current.usage.rateLimitedCalls / current.usage.totalCalls;

    // Update cache metrics if applicable
    if (context?.metadata.cacheHit !== undefined) {
      if (context.metadata.cacheHit) {
        current.caching.totalHits++;
        current.caching.averageHitLatency =
          (current.caching.averageHitLatency * (current.caching.totalHits - 1) +
            executionTime) /
          current.caching.totalHits;
      } else {
        current.caching.totalMisses++;
        current.caching.averageMissLatency =
          (current.caching.averageMissLatency *
            (current.caching.totalMisses - 1) +
            executionTime) /
          current.caching.totalMisses;
      }

      const totalCacheRequests =
        current.caching.totalHits + current.caching.totalMisses;
      current.caching.hitRate = current.caching.totalHits / totalCacheRequests;
      current.caching.missRate =
        current.caching.totalMisses / totalCacheRequests;
    }

    // Update error tracking
    if (!success && context?.errorCode) {
      current.errors.byCode[context.errorCode] =
        (current.errors.byCode[context.errorCode] || 0) + 1;

      if (context.error) {
        const errorEntry = current.errors.topErrors.find(
          (e) => e.error === context.error,
        );
        if (errorEntry) {
          errorEntry.count++;
          errorEntry.lastOccurrence = new Date();
        } else {
          current.errors.topErrors.push({
            error: context.error,
            count: 1,
            lastOccurrence: new Date(),
          });
        }

        // Keep only top 10 errors
        current.errors.topErrors.sort((a, b) => b.count - a.count);
        current.errors.topErrors = current.errors.topErrors.slice(0, 10);
      }
    }

    // Update resource usage if available
    if (context?.metadata.memoryUsage) {
      current.resources.averageMemoryUsage =
        (current.resources.averageMemoryUsage * (current.usage.totalCalls - 1) +
          context.metadata.memoryUsage) /
        current.usage.totalCalls;
      current.resources.peakMemoryUsage = Math.max(
        current.resources.peakMemoryUsage,
        context.metadata.memoryUsage,
      );
    }

    if (context?.metadata.cpuUsage) {
      current.resources.averageCpuUsage =
        (current.resources.averageCpuUsage * (current.usage.totalCalls - 1) +
          context.metadata.cpuUsage) /
        current.usage.totalCalls;
      current.resources.peakCpuUsage = Math.max(
        current.resources.peakCpuUsage,
        context.metadata.cpuUsage,
      );
    }

    this.analytics.set(toolId, current);

    // Emit analytics update via WebSocket
    wsService.send("tool:analytics_update", { toolId, analytics: current });
  }

  public async testTool(
    toolConfig: ToolConfig,
    input: any,
    sessionId: string,
  ): Promise<ToolTestResult> {
    const startTime = Date.now();

    try {
      const executionContext = await this.executeToolWithContext(
        toolConfig,
        input,
        sessionId,
      );
      const endTime = Date.now();

      return {
        success: executionContext.status === "completed",
        executionTime: endTime - startTime,
        input,
        output: executionContext.output,
        error: executionContext.error,
        performanceMetrics: {
          latency: executionContext.metadata.executionTime || 0,
          memoryUsage: executionContext.metadata.memoryUsage || 0,
          cpuUsage: executionContext.metadata.cpuUsage || 0,
        },
        timestamp: new Date(),
      };
    } catch (error: any) {
      const endTime = Date.now();

      return {
        success: false,
        executionTime: endTime - startTime,
        input,
        error: error.message,
        performanceMetrics: {
          latency: endTime - startTime,
          memoryUsage: 0,
          cpuUsage: 0,
        },
        timestamp: new Date(),
      };
    }
  }

  public getExecutionContext(
    contextId: string,
  ): ToolExecutionContext | undefined {
    return this.executionContexts.get(contextId);
  }

  public getAnalytics(toolId: string): any {
    return this.analytics.get(toolId);
  }

  public onEvent(eventType: string, handler: (event: ToolEvent) => void) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  public offEvent(eventType: string, handler: (event: ToolEvent) => void) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emitEvent(event: ToolEvent) {
    const handlers = this.eventHandlers.get(event.type) || [];
    handlers.forEach((handler) => {
      try {
        handler(event);
      } catch (error) {
        console.error("Error in tool event handler:", error);
      }
    });

    // Also emit via WebSocket
    wsService.send("tool:event", event);
  }

  private generateContextId(): string {
    return `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private addExecutionEvent(
    context: ToolExecutionContext,
    type: string,
    message: string,
    data?: any,
  ): void {
    const event: ToolExecutionEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type,
      message,
      data,
    };

    context.events.push(event);

    // Keep only last 50 events per execution
    if (context.events.length > 50) {
      context.events = context.events.slice(-50);
    }
  }

  private startQueueProcessor(): void {
    setInterval(() => {
      if (!this.isProcessingQueue && this.executionQueue.length > 0) {
        this.processExecutionQueue();
      }
    }, 100);
  }

  private async processExecutionQueue(): Promise<void> {
    if (this.isProcessingQueue) return;

    this.isProcessingQueue = true;

    try {
      while (
        this.executionQueue.length > 0 &&
        this.activeExecutions.size <
          EXECUTION_CONSTANTS.MAX_CONCURRENT_EXECUTIONS
      ) {
        const { context, resolve, reject } = this.executionQueue.shift()!;

        try {
          const result = await this.executeToolWithContext(
            context as any, // This would need proper typing in production
            context.input,
            context.sessionId,
          );
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  private startPeriodicCleanup(): void {
    // Clean up old execution contexts every 5 minutes
    setInterval(
      () => {
        this.cleanup();
      },
      5 * 60 * 1000,
    );

    // Clean up cache every hour
    setInterval(
      () => {
        this.cache.clear();
      },
      60 * 60 * 1000,
    );
  }

  // Enhanced validation methods
  private async validateJsonSchema(
    schema: string,
    data: any,
  ): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const parsedSchema = JSON.parse(schema);

      // Basic JSON Schema validation - in production, use a proper validator like Ajv
      if (!parsedSchema.type || !parsedSchema.properties) {
        return {
          valid: false,
          errors: ["Schema must have type and properties"],
        };
      }

      const errors: string[] = [];

      // Validate required fields
      if (parsedSchema.required && Array.isArray(parsedSchema.required)) {
        for (const field of parsedSchema.required) {
          if (!(field in data)) {
            errors.push(`Required field '${field}' is missing`);
          }
        }
      }

      // Validate field types
      for (const [fieldName, fieldSchema] of Object.entries(
        parsedSchema.properties as Record<string, any>,
      )) {
        if (fieldName in data) {
          const fieldValue = data[fieldName];
          const expectedType = fieldSchema.type;

          if (
            expectedType &&
            typeof fieldValue !== expectedType &&
            expectedType !== "any"
          ) {
            errors.push(
              `Field '${fieldName}' should be of type '${expectedType}', got '${typeof fieldValue}'`,
            );
          }
        }
      }

      return {
        valid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      return { valid: false, errors: ["Invalid JSON schema format"] };
    }
  }

  // Enhanced security and monitoring
  public getExecutionMetrics(): {
    activeExecutions: number;
    queuedExecutions: number;
    totalExecutions: number;
    cacheSize: number;
  } {
    return {
      activeExecutions: this.activeExecutions.size,
      queuedExecutions: this.executionQueue.length,
      totalExecutions: this.executionContexts.size,
      cacheSize: this.cache.size(),
    };
  }

  public async cancelExecution(executionId: string): Promise<boolean> {
    const context = this.executionContexts.get(executionId);
    if (
      !context ||
      context.status === "completed" ||
      context.status === "failed"
    ) {
      return false;
    }

    context.status = "cancelled";
    context.endTime = new Date();
    context.metadata.executionTime =
      context.endTime.getTime() - context.startTime.getTime();

    this.addExecutionEvent(
      context,
      "execution_cancelled",
      "Execution cancelled by user request",
    );

    this.emitEvent({
      id: this.generateEventId(),
      type: "tool_call_cancelled",
      toolId: context.toolId,
      sessionId: context.sessionId,
      executionId,
      correlationId: context.correlationId,
      timestamp: new Date(),
      data: { reason: "user_request" },
      severity: "warning",
      source: "engine",
    });

    this.activeExecutions.delete(executionId);
    return true;
  }

  public cleanup() {
    // Clean up old execution contexts (older than 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    for (const [contextId, context] of this.executionContexts.entries()) {
      if (context.startTime < oneHourAgo) {
        this.executionContexts.delete(contextId);
      }
    }
  }
}

export const toolEngine = new ToolEngine();

// Cleanup old contexts every 30 minutes
setInterval(
  () => {
    toolEngine.cleanup();
  },
  30 * 60 * 1000,
);
