import {
  ToolAgentHybridConfig,
  HybridExecutionContext,
  AgentConfig,
  ToolConfig,
  ToolExecutionContext,
  SessionState,
  HybridAnalytics,
} from "@/types/api";
import { toolEngine } from "./toolEngine";
import { stateManager } from "./state";
import { wsService } from "./websocket";
import { apiService } from "./api";
import { z } from "zod";

// Production-grade constants for hybrid execution
const HYBRID_CONSTANTS = {
  MAX_CONCURRENT_HYBRIDS: 50,
  DEFAULT_TIMEOUT: 60000,
  MAX_TIMEOUT: 300000,
  MIN_TIMEOUT: 5000,
  MAX_TOOL_SEQUENCE_LENGTH: 20,
  MAX_CONTEXT_SIZE: 100000,
  DEFAULT_CONFIDENCE_THRESHOLD: 0.7,
  MAX_RETRY_ATTEMPTS: 3,
  REASONING_TIMEOUT: 30000,
  CONTEXT_PROPAGATION_LIMIT: 10000,
} as const;

// Enhanced error types for hybrid execution
class HybridExecutionError extends Error {
  constructor(
    message: string,
    public code: string,
    public category:
      | "validation"
      | "execution"
      | "timeout"
      | "resource"
      | "orchestration"
      | "agent"
      | "tool",
    public retryable: boolean = false,
    public details?: Record<string, any>,
  ) {
    super(message);
    this.name = "HybridExecutionError";
  }
}

// Agent reasoning simulator (in production, this would interface with actual LLM)
class AgentReasoningEngine {
  async reason(
    agentConfig: AgentConfig,
    input: any,
    context: Record<string, any>,
    availableTools: ToolConfig[],
    sessionState?: SessionState,
  ): Promise<{
    reasoning: {
      steps: Array<{
        step: number;
        thought: string;
        action?: string;
        observation?: string;
        timestamp: Date;
      }>;
      finalDecision: string;
      confidence: number;
    };
    toolsSelected: string[];
    executionPlan: {
      sequence: Array<{
        toolId: string;
        priority: number;
        conditions?: Record<string, any>;
        expectedOutput?: any;
      }>;
      parallelGroups?: string[][];
    };
  }> {
    const startTime = Date.now();

    // Simulate agent reasoning process
    const reasoningSteps = [
      {
        step: 1,
        thought: `Analyzing input: ${JSON.stringify(input).substring(0, 100)}...`,
        timestamp: new Date(),
      },
      {
        step: 2,
        thought: `Available tools: ${availableTools.map((t) => t.name).join(", ")}`,
        timestamp: new Date(),
      },
      {
        step: 3,
        thought:
          "Determining optimal tool sequence based on input requirements",
        action: "tool_selection",
        timestamp: new Date(),
      },
    ];

    // Simulate tool selection logic based on input analysis
    const selectedTools = this.selectOptimalTools(
      input,
      availableTools,
      context,
    );
    const executionPlan = this.createExecutionPlan(selectedTools, agentConfig);

    reasoningSteps.push({
      step: 4,
      thought: `Selected ${selectedTools.length} tools for execution`,
      observation: `Tools: ${selectedTools.map((t) => t.name).join(", ")}`,
      timestamp: new Date(),
    });

    const confidence = this.calculateConfidence(selectedTools, input, context);

    return {
      reasoning: {
        steps: reasoningSteps,
        finalDecision: `Execute ${selectedTools.length} tools in ${executionPlan.sequence.length > 1 ? "sequence" : "single step"}`,
        confidence,
      },
      toolsSelected: selectedTools.map((t) => t.id!),
      executionPlan,
    };
  }

  private selectOptimalTools(
    input: any,
    availableTools: ToolConfig[],
    context: Record<string, any>,
  ): ToolConfig[] {
    // Production implementation would use sophisticated ML models
    // For now, we'll use rule-based selection

    const inputString = JSON.stringify(input).toLowerCase();
    const contextString = JSON.stringify(context).toLowerCase();

    return availableTools
      .filter((tool) => {
        // Simple keyword matching - in production, use semantic similarity
        const toolDescription = (tool.description || "").toLowerCase();
        const toolName = tool.name.toLowerCase();

        // Check if tool is relevant to input
        const keywords = [toolName, ...toolDescription.split(" ")];
        return keywords.some(
          (keyword) =>
            inputString.includes(keyword) || contextString.includes(keyword),
        );
      })
      .slice(0, 5); // Limit to 5 tools max
  }

  private createExecutionPlan(
    tools: ToolConfig[],
    agentConfig: AgentConfig,
  ): {
    sequence: Array<{
      toolId: string;
      priority: number;
      conditions?: Record<string, any>;
      expectedOutput?: any;
    }>;
    parallelGroups?: string[][];
  } {
    const sequence = tools.map((tool, index) => ({
      toolId: tool.id!,
      priority: 100 - index * 10, // Higher priority for earlier tools
      conditions: {
        requiresPreviousSuccess: index > 0,
        timeout: tool.timeout || 30000,
      },
      expectedOutput: this.predictExpectedOutput(tool),
    }));

    // Determine if any tools can run in parallel
    const parallelGroups = this.identifyParallelGroups(tools);

    return { sequence, parallelGroups };
  }

  private predictExpectedOutput(tool: ToolConfig): any {
    try {
      const outputSchema = JSON.parse(tool.outputSchema);
      return {
        type: outputSchema.type || "object",
        expectedFields: Object.keys(outputSchema.properties || {}),
      };
    } catch {
      return { type: "unknown" };
    }
  }

  private identifyParallelGroups(tools: ToolConfig[]): string[][] {
    // Simple heuristic: tools with no dependencies can run in parallel
    const independentTools = tools.filter(
      (tool) =>
        !tool.hitl?.parameterBinding?.agentMemory &&
        !tool.hitl?.parameterBinding?.sessionContext,
    );

    if (independentTools.length > 1) {
      return [independentTools.map((t) => t.id!)];
    }

    return [];
  }

  private calculateConfidence(
    selectedTools: ToolConfig[],
    input: any,
    context: Record<string, any>,
  ): number {
    // Simple confidence calculation - in production, use ML models
    let confidence = 0.5; // Base confidence

    // Increase confidence based on tool relevance
    if (selectedTools.length > 0) {
      confidence += 0.2;
    }

    // Increase confidence if we have context
    if (Object.keys(context).length > 0) {
      confidence += 0.1;
    }

    // Increase confidence based on tool success rates (would come from analytics)
    confidence += 0.2;

    return Math.min(confidence, 1.0);
  }
}

// Main Hybrid Engine
class HybridEngine {
  private executionContexts: Map<string, HybridExecutionContext> = new Map();
  private analytics: Map<string, HybridAnalytics> = new Map();
  private activeExecutions: Set<string> = new Set();
  private agentReasoningEngine: AgentReasoningEngine =
    new AgentReasoningEngine();
  private eventHandlers: Map<string, ((event: any) => void)[]> = new Map();

  constructor() {
    this.setupEventListeners();
    this.startPeriodicCleanup();
  }

  private setupEventListeners() {
    wsService.on("hybrid:execution_update", (data) => {
      this.handleExecutionUpdate(data);
    });

    wsService.on("hybrid:cancel_request", (data) => {
      this.handleCancelRequest(data);
    });
  }

  private handleExecutionUpdate(data: {
    executionId: string;
    update: Partial<HybridExecutionContext>;
  }) {
    const context = this.executionContexts.get(data.executionId);
    if (context) {
      Object.assign(context, data.update);
      this.emitEvent("hybrid:execution_updated", {
        executionId: data.executionId,
        context,
      });
    }
  }

  private handleCancelRequest(data: { executionId: string }) {
    this.cancelExecution(data.executionId);
  }

  public async executeHybrid(
    hybridConfig: ToolAgentHybridConfig,
    input: any,
    sessionId: string,
    context: Record<string, any> = {},
  ): Promise<HybridExecutionContext> {
    // Validate input size
    const inputSize = JSON.stringify(input).length;
    if (inputSize > HYBRID_CONSTANTS.MAX_CONTEXT_SIZE) {
      throw new HybridExecutionError(
        `Input size ${inputSize} bytes exceeds maximum ${HYBRID_CONSTANTS.MAX_CONTEXT_SIZE} bytes`,
        "INPUT_TOO_LARGE",
        "validation",
      );
    }

    // Check concurrent execution limits
    if (this.activeExecutions.size >= HYBRID_CONSTANTS.MAX_CONCURRENT_HYBRIDS) {
      throw new HybridExecutionError(
        "Maximum concurrent hybrid executions reached",
        "CONCURRENT_LIMIT_EXCEEDED",
        "resource",
      );
    }

    const executionId = this.generateExecutionId();
    const correlationId = this.generateCorrelationId();

    // Initialize execution context
    const executionContext: HybridExecutionContext = {
      id: executionId,
      hybridId: hybridConfig.id!,
      sessionId,
      correlationId,
      startTime: new Date(),
      status: "pending",
      toolExecutions: [],
      hybridMetadata: {
        orchestrationMode: hybridConfig.orchestration.mode,
        totalToolsExecuted: 0,
        successfulTools: 0,
        failedTools: 0,
        skippedTools: 0,
        adaptiveBehaviorTriggered: false,
        fallbackUsed: false,
        contextPropagationSize: JSON.stringify(context).length,
        performanceMetrics: {
          totalLatency: 0,
          agentReasoningTime: 0,
          toolExecutionTime: 0,
          contextPropagationTime: 0,
          memoryUsage: 0,
          cpuUsage: 0,
        },
      },
      results: {
        success: false,
        output: null,
        confidence: 0,
        reasoning: "",
        toolResults: {},
      },
      events: [],
    };

    this.executionContexts.set(executionId, executionContext);
    this.activeExecutions.add(executionId);

    try {
      this.addEvent(
        executionContext,
        "execution_started",
        "system",
        "Hybrid execution initiated",
      );

      // Update status to running
      executionContext.status = "running";
      this.emitEvent("hybrid:execution_started", {
        executionId,
        hybridId: hybridConfig.id,
        sessionId,
      });

      // Step 1: Agent Reasoning Phase
      const reasoningStartTime = Date.now();
      const agentConfig = await this.resolveAgentConfig(hybridConfig);
      const availableTools = await this.resolveToolConfigs(hybridConfig);

      this.addEvent(
        executionContext,
        "agent_reasoning_started",
        "agent",
        "Starting agent reasoning phase",
      );

      const sessionState = stateManager.getState(sessionId);
      const agentReasoning = await this.agentReasoningEngine.reason(
        agentConfig,
        input,
        context,
        availableTools,
        sessionState || undefined,
      );

      const reasoningEndTime = Date.now();
      executionContext.hybridMetadata.performanceMetrics.agentReasoningTime =
        reasoningEndTime - reasoningStartTime;
      executionContext.agentExecution = agentReasoning;

      this.addEvent(
        executionContext,
        "agent_reasoning_completed",
        "agent",
        `Agent selected ${agentReasoning.toolsSelected.length} tools with ${(agentReasoning.reasoning.confidence * 100).toFixed(1)}% confidence`,
      );

      // Step 2: Tool Orchestration Phase
      const toolExecutionStartTime = Date.now();

      this.addEvent(
        executionContext,
        "tool_orchestration_started",
        "orchestrator",
        "Starting tool execution phase",
      );

      const toolResults = await this.executeToolSequence(
        executionContext,
        agentReasoning.executionPlan,
        availableTools,
        input,
        context,
        sessionId,
      );

      const toolExecutionEndTime = Date.now();
      executionContext.hybridMetadata.performanceMetrics.toolExecutionTime =
        toolExecutionEndTime - toolExecutionStartTime;

      // Step 3: Results Synthesis
      const synthesisStartTime = Date.now();

      this.addEvent(
        executionContext,
        "results_synthesis_started",
        "system",
        "Synthesizing final results",
      );

      const finalResults = await this.synthesizeResults(
        executionContext,
        agentReasoning,
        toolResults,
        input,
        context,
      );

      executionContext.results = finalResults;
      executionContext.status = "completed";
      executionContext.endTime = new Date();

      const totalLatency =
        executionContext.endTime.getTime() -
        executionContext.startTime.getTime();
      executionContext.hybridMetadata.performanceMetrics.totalLatency =
        totalLatency;

      this.addEvent(
        executionContext,
        "execution_completed",
        "system",
        `Hybrid execution completed successfully in ${totalLatency}ms`,
      );

      // Update analytics
      await this.updateAnalytics(hybridConfig.id!, executionContext);

      // Emit completion event
      this.emitEvent("hybrid:execution_completed", {
        executionId,
        results: finalResults,
        performance: executionContext.hybridMetadata.performanceMetrics,
      });

      return executionContext;
    } catch (error: any) {
      // Handle execution error
      executionContext.status = "failed";
      executionContext.error = error.message;
      executionContext.errorCode = error.code || "UNKNOWN_ERROR";
      executionContext.stackTrace = error.stack;
      executionContext.endTime = new Date();

      const totalLatency =
        executionContext.endTime.getTime() -
        executionContext.startTime.getTime();
      executionContext.hybridMetadata.performanceMetrics.totalLatency =
        totalLatency;

      this.addEvent(
        executionContext,
        "execution_failed",
        "system",
        `Hybrid execution failed: ${error.message}`,
        "error",
      );

      // Try fallback strategy if configured
      if (hybridConfig.orchestration.fallbackStrategy !== "none") {
        try {
          this.addEvent(
            executionContext,
            "fallback_triggered",
            "system",
            `Attempting fallback strategy: ${hybridConfig.orchestration.fallbackStrategy}`,
          );

          const fallbackResults = await this.executeFallback(
            hybridConfig,
            executionContext,
            input,
            context,
            sessionId,
            error,
          );

          executionContext.results = fallbackResults;
          executionContext.status = "completed";
          executionContext.hybridMetadata.fallbackUsed = true;

          this.addEvent(
            executionContext,
            "fallback_succeeded",
            "system",
            "Fallback strategy executed successfully",
          );
        } catch (fallbackError: any) {
          this.addEvent(
            executionContext,
            "fallback_failed",
            "system",
            `Fallback strategy failed: ${fallbackError.message}`,
            "error",
          );
        }
      }

      // Update analytics even for failures
      await this.updateAnalytics(hybridConfig.id!, executionContext);

      // Emit error event
      this.emitEvent("hybrid:execution_failed", {
        executionId,
        error: error.message,
        errorCode: executionContext.errorCode,
      });

      return executionContext;
    } finally {
      // Always clean up
      this.activeExecutions.delete(executionId);
    }
  }

  private async resolveAgentConfig(
    hybridConfig: ToolAgentHybridConfig,
  ): Promise<AgentConfig> {
    if (hybridConfig.agentConfig.inline) {
      return hybridConfig.agentConfig.inline;
    } else if (hybridConfig.agentConfig.agentId) {
      // In production, fetch from API
      const response = await apiService.request(
        `/agents/${hybridConfig.agentConfig.agentId}`,
      );
      if (response.success && response.data) {
        return response.data;
      }
    }

    throw new HybridExecutionError(
      "No valid agent configuration found",
      "INVALID_AGENT_CONFIG",
      "validation",
    );
  }

  private async resolveToolConfigs(
    hybridConfig: ToolAgentHybridConfig,
  ): Promise<ToolConfig[]> {
    const tools: ToolConfig[] = [];

    for (const toolConfig of hybridConfig.toolConfigs) {
      if (toolConfig.inline) {
        tools.push(toolConfig.inline);
      } else if (toolConfig.toolId) {
        // In production, fetch from API
        try {
          const response = await apiService.request(
            `/tools/${toolConfig.toolId}`,
          );
          if (response.success && response.data) {
            tools.push(response.data);
          }
        } catch (error) {
          console.warn(`Failed to resolve tool ${toolConfig.toolId}:`, error);
        }
      }
    }

    if (tools.length === 0) {
      throw new HybridExecutionError(
        "No valid tool configurations found",
        "INVALID_TOOL_CONFIG",
        "validation",
      );
    }

    return tools;
  }

  private async executeToolSequence(
    executionContext: HybridExecutionContext,
    executionPlan: any,
    availableTools: ToolConfig[],
    input: any,
    context: Record<string, any>,
    sessionId: string,
  ): Promise<Record<string, any>> {
    const toolResults: Record<string, any> = {};
    const propagatedContext = { ...context };

    // Execute tools based on orchestration mode
    switch (executionContext.hybridMetadata.orchestrationMode) {
      case "sequential":
        return await this.executeSequential(
          executionContext,
          executionPlan,
          availableTools,
          input,
          propagatedContext,
          sessionId,
        );
      case "parallel":
        return await this.executeParallel(
          executionContext,
          executionPlan,
          availableTools,
          input,
          propagatedContext,
          sessionId,
        );
      case "conditional":
        return await this.executeConditional(
          executionContext,
          executionPlan,
          availableTools,
          input,
          propagatedContext,
          sessionId,
        );
      case "dynamic":
        return await this.executeDynamic(
          executionContext,
          executionPlan,
          availableTools,
          input,
          propagatedContext,
          sessionId,
        );
      default:
        throw new HybridExecutionError(
          `Unsupported orchestration mode: ${executionContext.hybridMetadata.orchestrationMode}`,
          "INVALID_ORCHESTRATION_MODE",
          "orchestration",
        );
    }
  }

  private async executeSequential(
    executionContext: HybridExecutionContext,
    executionPlan: any,
    availableTools: ToolConfig[],
    input: any,
    context: Record<string, any>,
    sessionId: string,
  ): Promise<Record<string, any>> {
    const toolResults: Record<string, any> = {};

    for (const step of executionPlan.sequence) {
      const tool = availableTools.find((t) => t.id === step.toolId);
      if (!tool) {
        this.addEvent(
          executionContext,
          "tool_not_found",
          "orchestrator",
          `Tool ${step.toolId} not found, skipping`,
          "warning",
        );
        continue;
      }

      try {
        this.addEvent(
          executionContext,
          "tool_execution_started",
          "tool",
          `Starting execution of tool: ${tool.name}`,
        );

        const toolExecution = {
          toolId: step.toolId,
          executionId: this.generateExecutionId(),
          status: "running" as const,
          startTime: new Date(),
          input: { ...input, ...context },
          metadata: {
            retryCount: 0,
            triggeredBy: "agent" as const,
            priority: step.priority,
          },
        };

        executionContext.toolExecutions.push(toolExecution);

        // Execute tool using existing tool engine
        const toolExecutionContext = await toolEngine.executeToolWithContext(
          tool,
          toolExecution.input,
          sessionId,
          {
            sessionContext: context,
            agentMemory: executionContext.agentExecution?.reasoning.steps,
          },
        );

        toolExecution.status =
          toolExecutionContext.status === "completed" ? "completed" : "failed";
        toolExecution.endTime = new Date();
        toolExecution.output = toolExecutionContext.output;
        toolExecution.error = toolExecutionContext.error;

        if (toolExecution.status === "completed") {
          toolResults[step.toolId] = toolExecution.output;
          executionContext.hybridMetadata.successfulTools++;

          // Propagate results to context for next tools
          context[`tool_${tool.name}_result`] = toolExecution.output;

          this.addEvent(
            executionContext,
            "tool_execution_completed",
            "tool",
            `Tool ${tool.name} executed successfully`,
          );
        } else {
          executionContext.hybridMetadata.failedTools++;

          this.addEvent(
            executionContext,
            "tool_execution_failed",
            "tool",
            `Tool ${tool.name} execution failed: ${toolExecution.error}`,
            "error",
          );

          // Check if we should continue or fail fast
          if (step.conditions?.requiresPreviousSuccess) {
            throw new HybridExecutionError(
              `Tool ${tool.name} failed and is required for continuation`,
              "REQUIRED_TOOL_FAILED",
              "tool",
            );
          }
        }

        executionContext.hybridMetadata.totalToolsExecuted++;
      } catch (error: any) {
        this.addEvent(
          executionContext,
          "tool_execution_error",
          "tool",
          `Tool ${tool.name} execution error: ${error.message}`,
          "error",
        );

        executionContext.hybridMetadata.failedTools++;
        executionContext.hybridMetadata.totalToolsExecuted++;

        // Continue with next tool unless it's a critical error
        if (error.category === "resource" || error.category === "timeout") {
          throw error;
        }
      }
    }

    return toolResults;
  }

  private async executeParallel(
    executionContext: HybridExecutionContext,
    executionPlan: any,
    availableTools: ToolConfig[],
    input: any,
    context: Record<string, any>,
    sessionId: string,
  ): Promise<Record<string, any>> {
    const toolResults: Record<string, any> = {};

    // Execute all tools in parallel
    const toolPromises = executionPlan.sequence.map(async (step: any) => {
      const tool = availableTools.find((t) => t.id === step.toolId);
      if (!tool) return null;

      try {
        const toolExecution = {
          toolId: step.toolId,
          executionId: this.generateExecutionId(),
          status: "running" as const,
          startTime: new Date(),
          input: { ...input, ...context },
          metadata: {
            retryCount: 0,
            triggeredBy: "agent" as const,
            priority: step.priority,
          },
        };

        executionContext.toolExecutions.push(toolExecution);

        const toolExecutionContext = await toolEngine.executeToolWithContext(
          tool,
          toolExecution.input,
          sessionId,
        );

        toolExecution.status =
          toolExecutionContext.status === "completed" ? "completed" : "failed";
        toolExecution.endTime = new Date();
        toolExecution.output = toolExecutionContext.output;
        toolExecution.error = toolExecutionContext.error;

        return {
          toolId: step.toolId,
          result: toolExecution.output,
          success: toolExecution.status === "completed",
          error: toolExecution.error,
        };
      } catch (error: any) {
        return {
          toolId: step.toolId,
          result: null,
          success: false,
          error: error.message,
        };
      }
    });

    const results = await Promise.allSettled(toolPromises);

    results.forEach((result, index) => {
      if (result.status === "fulfilled" && result.value) {
        const toolResult = result.value;
        if (toolResult.success) {
          toolResults[toolResult.toolId] = toolResult.result;
          executionContext.hybridMetadata.successfulTools++;
        } else {
          executionContext.hybridMetadata.failedTools++;
        }
        executionContext.hybridMetadata.totalToolsExecuted++;
      }
    });

    return toolResults;
  }

  private async executeConditional(
    executionContext: HybridExecutionContext,
    executionPlan: any,
    availableTools: ToolConfig[],
    input: any,
    context: Record<string, any>,
    sessionId: string,
  ): Promise<Record<string, any>> {
    // For now, fall back to sequential execution
    // In production, implement conditional logic based on rules
    return this.executeSequential(
      executionContext,
      executionPlan,
      availableTools,
      input,
      context,
      sessionId,
    );
  }

  private async executeDynamic(
    executionContext: HybridExecutionContext,
    executionPlan: any,
    availableTools: ToolConfig[],
    input: any,
    context: Record<string, any>,
    sessionId: string,
  ): Promise<Record<string, any>> {
    // For now, fall back to sequential execution
    // In production, implement dynamic orchestration based on real-time conditions
    return this.executeSequential(
      executionContext,
      executionPlan,
      availableTools,
      input,
      context,
      sessionId,
    );
  }

  private async synthesizeResults(
    executionContext: HybridExecutionContext,
    agentReasoning: any,
    toolResults: Record<string, any>,
    input: any,
    context: Record<string, any>,
  ): Promise<{
    success: boolean;
    output: any;
    confidence: number;
    reasoning: string;
    toolResults: Record<string, any>;
    agentInsights?: string;
    recommendations?: string[];
  }> {
    const successfulTools = Object.keys(toolResults).length;
    const totalTools = executionContext.hybridMetadata.totalToolsExecuted;
    const successRate = totalTools > 0 ? successfulTools / totalTools : 0;

    // Synthesize final output from tool results
    const synthesizedOutput = this.combineToolResults(toolResults, input);

    // Calculate final confidence based on agent confidence and tool success rate
    const finalConfidence =
      agentReasoning.reasoning.confidence * 0.6 + successRate * 0.4;

    // Generate reasoning explanation
    const reasoning =
      `Agent selected ${agentReasoning.toolsSelected.length} tools with ${(agentReasoning.reasoning.confidence * 100).toFixed(1)}% confidence. ` +
      `${successfulTools} of ${totalTools} tools executed successfully (${(successRate * 100).toFixed(1)}% success rate).`;

    // Generate insights and recommendations
    const insights = this.generateInsights(executionContext, toolResults);
    const recommendations = this.generateRecommendations(
      executionContext,
      successRate,
    );

    return {
      success: successRate > 0.5, // Consider successful if more than 50% of tools succeeded
      output: synthesizedOutput,
      confidence: finalConfidence,
      reasoning,
      toolResults,
      agentInsights: insights,
      recommendations,
    };
  }

  private combineToolResults(
    toolResults: Record<string, any>,
    input: any,
  ): any {
    // Simple result combination - in production, use sophisticated synthesis
    if (Object.keys(toolResults).length === 0) {
      return { message: "No tools executed successfully", input };
    }

    if (Object.keys(toolResults).length === 1) {
      return Object.values(toolResults)[0];
    }

    // Combine multiple results
    return {
      combinedResults: toolResults,
      summary: `Successfully executed ${Object.keys(toolResults).length} tools`,
      input,
    };
  }

  private generateInsights(
    executionContext: HybridExecutionContext,
    toolResults: Record<string, any>,
  ): string {
    const insights = [];

    if (executionContext.hybridMetadata.successfulTools > 0) {
      insights.push(
        `${executionContext.hybridMetadata.successfulTools} tools executed successfully`,
      );
    }

    if (executionContext.hybridMetadata.failedTools > 0) {
      insights.push(
        `${executionContext.hybridMetadata.failedTools} tools failed`,
      );
    }

    const totalTime =
      executionContext.hybridMetadata.performanceMetrics.totalLatency;
    if (totalTime > 0) {
      insights.push(`Total execution time: ${totalTime}ms`);
    }

    return insights.join(". ");
  }

  private generateRecommendations(
    executionContext: HybridExecutionContext,
    successRate: number,
  ): string[] {
    const recommendations = [];

    if (successRate < 0.5) {
      recommendations.push(
        "Consider reviewing tool configurations or input parameters",
      );
    }

    if (
      executionContext.hybridMetadata.performanceMetrics.totalLatency > 30000
    ) {
      recommendations.push(
        "Consider optimizing tool execution or using parallel orchestration",
      );
    }

    if (executionContext.hybridMetadata.failedTools > 0) {
      recommendations.push(
        "Review failed tool logs and consider implementing retry mechanisms",
      );
    }

    return recommendations;
  }

  private async executeFallback(
    hybridConfig: ToolAgentHybridConfig,
    executionContext: HybridExecutionContext,
    input: any,
    context: Record<string, any>,
    sessionId: string,
    originalError: Error,
  ): Promise<any> {
    switch (hybridConfig.orchestration.fallbackStrategy) {
      case "agent_only":
        // Execute only agent reasoning without tools
        return {
          success: true,
          output: {
            message: "Fallback: Agent-only execution",
            reasoning: "Tools failed, using agent reasoning only",
          },
          confidence: 0.3,
          reasoning: "Fallback to agent-only execution due to tool failures",
          toolResults: {},
        };

      case "tools_only":
        // Try to execute tools without agent reasoning
        const availableTools = await this.resolveToolConfigs(hybridConfig);
        const simpleResults = await this.executeSimpleToolSequence(
          availableTools,
          input,
          sessionId,
        );
        return {
          success: Object.keys(simpleResults).length > 0,
          output: simpleResults,
          confidence: 0.4,
          reasoning: "Fallback to tools-only execution",
          toolResults: simpleResults,
        };

      case "best_effort":
        // Return partial results if any tools succeeded
        const partialResults = executionContext.toolExecutions
          .filter((t) => t.status === "completed")
          .reduce((acc, t) => ({ ...acc, [t.toolId]: t.output }), {});

        return {
          success: Object.keys(partialResults).length > 0,
          output: partialResults,
          confidence: 0.2,
          reasoning: "Fallback: Partial execution results",
          toolResults: partialResults,
        };

      default:
        throw originalError;
    }
  }

  private async executeSimpleToolSequence(
    tools: ToolConfig[],
    input: any,
    sessionId: string,
  ): Promise<Record<string, any>> {
    const results: Record<string, any> = {};

    for (const tool of tools.slice(0, 3)) {
      // Limit to first 3 tools
      try {
        const toolExecutionContext = await toolEngine.executeToolWithContext(
          tool,
          input,
          sessionId,
        );

        if (toolExecutionContext.status === "completed") {
          results[tool.id!] = toolExecutionContext.output;
        }
      } catch (error) {
        // Continue with next tool
        continue;
      }
    }

    return results;
  }

  private async updateAnalytics(
    hybridId: string,
    executionContext: HybridExecutionContext,
  ) {
    const current =
      this.analytics.get(hybridId) || this.createEmptyAnalytics(hybridId);

    // Update execution statistics
    current.execution.totalExecutions++;
    if (executionContext.status === "completed") {
      current.execution.successfulExecutions++;
    } else {
      current.execution.failedExecutions++;
    }

    const executionTime =
      executionContext.hybridMetadata.performanceMetrics.totalLatency;
    if (executionTime > 0) {
      current.execution.averageExecutionTime =
        (current.execution.averageExecutionTime *
          (current.execution.totalExecutions - 1) +
          executionTime) /
        current.execution.totalExecutions;
    }

    // Update agent performance
    if (executionContext.agentExecution) {
      current.agentPerformance.averageReasoningTime =
        (current.agentPerformance.averageReasoningTime +
          executionContext.hybridMetadata.performanceMetrics
            .agentReasoningTime) /
        2;
      current.agentPerformance.decisionConfidence =
        (current.agentPerformance.decisionConfidence +
          executionContext.agentExecution.reasoning.confidence) /
        2;
    }

    // Update tool utilization
    executionContext.toolExecutions.forEach((toolExec) => {
      const existingTool = current.toolUtilization.mostUsedTools.find(
        (t) => t.toolId === toolExec.toolId,
      );
      if (existingTool) {
        existingTool.usageCount++;
        if (toolExec.status === "completed") {
          existingTool.successRate =
            (existingTool.successRate * (existingTool.usageCount - 1) + 1) /
            existingTool.usageCount;
        } else {
          existingTool.successRate =
            (existingTool.successRate * (existingTool.usageCount - 1)) /
            existingTool.usageCount;
        }
      } else {
        current.toolUtilization.mostUsedTools.push({
          toolId: toolExec.toolId,
          usageCount: 1,
          successRate: toolExec.status === "completed" ? 1 : 0,
          averageExecutionTime: 0,
        });
      }
    });

    current.lastUpdated = new Date();
    this.analytics.set(hybridId, current);

    // Emit analytics update
    wsService.send("hybrid:analytics_update", { hybridId, analytics: current });
  }

  private createEmptyAnalytics(hybridId: string): HybridAnalytics {
    return {
      hybridId,
      timeRange: { start: new Date(), end: new Date() },
      execution: {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0,
        medianExecutionTime: 0,
        p95ExecutionTime: 0,
        throughput: 0,
      },
      agentPerformance: {
        averageReasoningTime: 0,
        toolSelectionAccuracy: 0,
        decisionConfidence: 0,
        adaptiveBehaviorTriggers: 0,
        memoryUtilization: 0,
      },
      toolUtilization: {
        mostUsedTools: [],
        toolSuccessRates: {},
        parallelExecutionEfficiency: 0,
      },
      resources: {
        averageMemoryUsage: 0,
        peakMemoryUsage: 0,
        averageCpuUsage: 0,
        peakCpuUsage: 0,
        networkBandwidth: 0,
        storageUtilization: 0,
      },
      errors: {
        byCategory: {},
        byComponent: { agent: 0, tool: 0, orchestrator: 0 },
        topErrors: [],
        recoveryRate: 0,
      },
      trends: {
        executionTimetrend: "stable",
        successRateTrend: "stable",
        resourceUsageTrend: "stable",
        userSatisfactionTrend: "stable",
      },
      lastUpdated: new Date(),
    };
  }

  public async cancelExecution(executionId: string): Promise<boolean> {
    const context = this.executionContexts.get(executionId);
    if (
      !context ||
      context.status === "completed" ||
      context.status === "failed"
    ) {
      return false;
    }

    context.status = "cancelled";
    context.endTime = new Date();

    this.addEvent(
      context,
      "execution_cancelled",
      "system",
      "Execution cancelled by user request",
    );

    this.activeExecutions.delete(executionId);

    this.emitEvent("hybrid:execution_cancelled", { executionId });

    return true;
  }

  public getExecutionContext(
    executionId: string,
  ): HybridExecutionContext | undefined {
    return this.executionContexts.get(executionId);
  }

  public getAnalytics(hybridId: string): HybridAnalytics | undefined {
    return this.analytics.get(hybridId);
  }

  public onEvent(eventType: string, handler: (event: any) => void) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  public offEvent(eventType: string, handler: (event: any) => void) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emitEvent(eventType: string, data: any) {
    const handlers = this.eventHandlers.get(eventType) || [];
    handlers.forEach((handler) => {
      try {
        handler(data);
      } catch (error) {
        console.error("Error in hybrid event handler:", error);
      }
    });

    // Also emit via WebSocket
    wsService.send(eventType, data);
  }

  private addEvent(
    context: HybridExecutionContext,
    type: string,
    source: "agent" | "tool" | "orchestrator" | "system",
    message: string,
    severity: "info" | "warning" | "error" | "critical" = "info",
  ) {
    const event = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type,
      source,
      message,
      severity,
    };

    context.events.push(event);

    // Keep only last 100 events per execution
    if (context.events.length > 100) {
      context.events = context.events.slice(-100);
    }
  }

  private generateExecutionId(): string {
    return `hybrid_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCorrelationId(): string {
    return `hybrid_corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `hybrid_evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private startPeriodicCleanup() {
    // Clean up old execution contexts every 10 minutes
    setInterval(
      () => {
        this.cleanup();
      },
      10 * 60 * 1000,
    );
  }

  public cleanup() {
    // Clean up old execution contexts (older than 2 hours)
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);

    for (const [executionId, context] of this.executionContexts.entries()) {
      if (context.startTime < twoHoursAgo) {
        this.executionContexts.delete(executionId);
      }
    }
  }

  public getExecutionMetrics(): {
    activeExecutions: number;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
  } {
    const totalExecutions = this.executionContexts.size;
    let successfulExecutions = 0;
    let failedExecutions = 0;

    for (const context of this.executionContexts.values()) {
      if (context.status === "completed") {
        successfulExecutions++;
      } else if (context.status === "failed") {
        failedExecutions++;
      }
    }

    return {
      activeExecutions: this.activeExecutions.size,
      totalExecutions,
      successfulExecutions,
      failedExecutions,
    };
  }
}

export const hybridEngine = new HybridEngine();

// Cleanup old contexts every hour
setInterval(
  () => {
    hybridEngine.cleanup();
  },
  60 * 60 * 1000,
);
