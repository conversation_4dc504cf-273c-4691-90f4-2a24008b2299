import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  PlusCircle,
  Search,
  Code,
  Play,
  Save,
  Trash2,
  Edit,
  Check,
  Loader2,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Globe,
  Clock,
  Activity,
  BarChart3,
  Zap,
  Database,
  Settings,
  TestTube,
  FileText,
  Copy,
  Download,
  Upload,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Timer,
  Shield,
  Cpu,
  HardDrive,
  Network,
  Eye,
  EyeOff,
  Code2,
  Braces,
  Terminal,
  Webhook,
  Link,
  Target,
  Layers,
  Trash,
} from "lucide-react";
import {
  ToolConfig,
  ToolConfigSchema,
  ToolExecutionContext,
  ToolAnalytics,
  ToolTestResult,
  ParameterInjectionContext,
} from "@/types/api";
import { apiService } from "@/services/api";
import { stateManager } from "@/services/state";
import { toolEngine } from "@/services/toolEngine";
import { agentEngine } from "@/services/agentEngine";
import { useSessionState, useRealTimeData } from "@/hooks/useRealTimeData";

const ToolManager: React.FC = () => {
  const { toast } = useToast();
  const [tools, setTools] = useState<ToolConfig[]>([]);
  const [selectedTool, setSelectedTool] = useState<ToolConfig | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<ToolTestResult | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("configuration");
  const [previewMode, setPreviewMode] = useState(false);
  const [testInput, setTestInput] = useState("");
  const [executionHistory, setExecutionHistory] = useState<
    ToolExecutionContext[]
  >([]);
  const [analytics, setAnalytics] = useState<ToolAnalytics | null>(null);
  const [realTimeEvents, setRealTimeEvents] = useState<any[]>([]);
  const [schemaValidation, setSchemaValidation] = useState<{
    input: boolean;
    output: boolean;
  }>({ input: true, output: true });
  const [parameterBindingContext, setParameterBindingContext] =
    useState<ParameterInjectionContext>({});
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [exportFormat, setExportFormat] = useState<"json" | "yaml" | "openapi">(
    "json",
  );

  const [sessionId] = useState(() =>
    stateManager.createSession(undefined, selectedTool?.id),
  );
  const { state: sessionState } = useSessionState(sessionId);
  const { data: toolEvents } = useRealTimeData("tool:events", []);

  // Load tools and setup real-time subscriptions
  useEffect(() => {
    const loadTools = async () => {
      try {
        setLoading(true);
        const response = await apiService.getTools();
        if (response.success && response.data) {
          setTools(response.data);
        }
      } catch (error) {
        console.error("Failed to load tools:", error);
        toast({
          title: "Error",
          description: "Failed to load tools",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadTools();
  }, [toast]);

  // Setup tool event listeners
  useEffect(() => {
    const handleToolEvent = (event: any) => {
      setRealTimeEvents((prev) => [event, ...prev.slice(0, 49)]); // Keep last 50 events

      if (selectedTool && event.toolId === selectedTool.id) {
        // Update analytics if this is for the selected tool
        if (
          event.type === "tool_call_result" ||
          event.type === "tool_call_error"
        ) {
          loadToolAnalytics(selectedTool.id!);
        }
      }
    };

    toolEngine.onEvent("tool_call_start", handleToolEvent);
    toolEngine.onEvent("tool_call_result", handleToolEvent);
    toolEngine.onEvent("tool_call_error", handleToolEvent);
    toolEngine.onEvent("tool_call_timeout", handleToolEvent);

    return () => {
      toolEngine.offEvent("tool_call_start", handleToolEvent);
      toolEngine.offEvent("tool_call_result", handleToolEvent);
      toolEngine.offEvent("tool_call_error", handleToolEvent);
      toolEngine.offEvent("tool_call_timeout", handleToolEvent);
    };
  }, [selectedTool]);

  // Validate tool configuration whenever it changes
  useEffect(() => {
    if (selectedTool) {
      validateToolConfiguration(selectedTool);
    }
  }, [selectedTool]);

  // Load analytics and execution history when tool is selected
  useEffect(() => {
    if (selectedTool?.id) {
      loadToolAnalytics(selectedTool.id);
      loadExecutionHistory(selectedTool.id);
    }
  }, [selectedTool?.id]);

  const validateToolConfiguration = async (tool: ToolConfig) => {
    try {
      ToolConfigSchema.parse(tool);

      // Validate schemas
      const inputValidation = await validateJsonSchema(tool.inputSchema);
      const outputValidation = await validateJsonSchema(tool.outputSchema);

      setSchemaValidation({
        input: inputValidation.valid,
        output: outputValidation.valid,
      });

      const errors: string[] = [];
      if (!inputValidation.valid) {
        errors.push(
          ...(inputValidation.errors || []).map((e) => `Input Schema: ${e}`),
        );
      }
      if (!outputValidation.valid) {
        errors.push(
          ...(outputValidation.errors || []).map((e) => `Output Schema: ${e}`),
        );
      }

      setValidationErrors(errors);
    } catch (error: any) {
      const errors = error.errors?.map(
        (e: any) => `${e.path.join(".")}: ${e.message}`,
      ) || ["Invalid configuration"];
      setValidationErrors(errors);
      setSchemaValidation({ input: false, output: false });
    }
  };

  const validateJsonSchema = async (
    schema: string,
  ): Promise<{ valid: boolean; errors?: string[] }> => {
    try {
      JSON.parse(schema);
      // In production, you would validate against JSON Schema spec
      return { valid: true };
    } catch (error) {
      return { valid: false, errors: ["Invalid JSON format"] };
    }
  };

  const loadToolAnalytics = async (toolId: string) => {
    try {
      const response = await apiService.getToolAnalytics(toolId);
      if (response.success && response.data) {
        setAnalytics(response.data);
      }
    } catch (error) {
      console.error("Failed to load analytics:", error);
    }
  };

  const loadExecutionHistory = async (toolId: string) => {
    try {
      const response = await apiService.getToolExecutionHistory(toolId, 20);
      if (response.success && response.data) {
        setExecutionHistory(response.data);
      }
    } catch (error) {
      console.error("Failed to load execution history:", error);
    }
  };

  const handleSelectTool = (tool: ToolConfig) => {
    setSelectedTool(tool);
    setIsEditing(false);
    setTestResult(null);
    setActiveTab("configuration");

    // Generate sample test input based on schema
    if (tool.inputSchema) {
      try {
        const schema = JSON.parse(tool.inputSchema);
        const sampleInput = generateSampleInput(schema);
        setTestInput(JSON.stringify(sampleInput, null, 2));
      } catch (error) {
        setTestInput("{}");
      }
    }
  };

  const generateSampleInput = (schema: any): any => {
    if (!schema.properties) return {};

    const sample: any = {};
    Object.entries(schema.properties).forEach(([key, prop]: [string, any]) => {
      switch (prop.type) {
        case "string":
          sample[key] = prop.example || `sample_${key}`;
          break;
        case "number":
          sample[key] = prop.example || 42;
          break;
        case "boolean":
          sample[key] = prop.example !== undefined ? prop.example : true;
          break;
        case "array":
          sample[key] = prop.example || ["item1", "item2"];
          break;
        case "object":
          sample[key] = prop.example || {};
          break;
        default:
          sample[key] = prop.example || null;
      }
    });

    return sample;
  };

  const handleCreateNewTool = () => {
    const newTool: ToolConfig = {
      name: "New Tool",
      inputSchema: JSON.stringify(
        {
          type: "object",
          properties: {
            input: {
              type: "string",
              description: "Input parameter",
              example: "Hello World",
            },
          },
          required: ["input"],
        },
        null,
        2,
      ),
      outputSchema: JSON.stringify(
        {
          type: "object",
          properties: {
            result: {
              type: "string",
              description: "Output result",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              description: "Execution timestamp",
            },
          },
        },
        null,
        2,
      ),
      validationRules: "",
      endpoint: "",
      method: "POST",
      headers: {},
      timeout: 30000,
      retryAttempts: 3,
      hitl: {
        enabled: false,
        mode: "optional",
        timeout: 300,
        fallbackStrategy: "none",
        parameterBinding: {
          sessionContext: false,
          userContext: false,
          agentMemory: false,
          customBindings: {
            user_id: "user_id",
            session_id: "session_id",
            user_email: "user_email",
            user_name: "user_name",
            user_role: "user_role",
          },
        },
      },
    };

    setSelectedTool(newTool);
    setIsEditing(true);
    setTestResult(null);
    setActiveTab("configuration");
  };

  const handleSaveTool = async () => {
    if (!selectedTool) return;

    try {
      setLoading(true);

      // Validate configuration
      const validatedTool = ToolConfigSchema.parse({
        ...selectedTool,
        metadata: {
          ...selectedTool.metadata,
          updatedAt: new Date(),
        },
      });

      let response;
      if (selectedTool.id) {
        response = await apiService.updateTool(selectedTool.id, validatedTool);
      } else {
        response = await apiService.createTool({
          ...validatedTool,
          metadata: {
            ...validatedTool.metadata,
            createdAt: new Date(),
          },
        });
      }

      if (response.success && response.data) {
        const updatedTool = response.data;

        if (selectedTool.id) {
          setTools(
            tools.map((tool) =>
              tool.id === selectedTool.id ? updatedTool : tool,
            ),
          );
        } else {
          setTools([...tools, updatedTool]);
        }

        setSelectedTool(updatedTool);
        setIsEditing(false);

        toast({
          title: "Success",
          description: selectedTool.id
            ? "Tool updated successfully"
            : "Tool created successfully",
        });
      } else {
        throw new Error(response.error || "Failed to save tool");
      }
    } catch (error: any) {
      console.error("Failed to save tool:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save tool",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTool = async () => {
    if (!selectedTool || !selectedTool.id) return;

    try {
      setLoading(true);
      const response = await apiService.deleteTool(selectedTool.id);

      if (response.success) {
        setTools(tools.filter((tool) => tool.id !== selectedTool.id));
        setSelectedTool(null);
        toast({
          title: "Success",
          description: "Tool deleted successfully",
        });
      } else {
        throw new Error(response.error || "Failed to delete tool");
      }
    } catch (error: any) {
      console.error("Failed to delete tool:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete tool",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestTool = async () => {
    if (!selectedTool) return;

    try {
      setTesting(true);
      setTestResult(null);

      let input;
      try {
        input = JSON.parse(testInput);
      } catch (error) {
        throw new Error("Invalid JSON in test input");
      }

      // Validate input against schema before testing
      if (selectedTool.inputSchema) {
        const inputValidation = await validateJsonSchema(
          selectedTool.inputSchema,
        );
        if (!inputValidation.valid) {
          throw new Error(
            `Input schema validation failed: ${inputValidation.errors?.join(", ")}`,
          );
        }

        // Validate actual input against schema
        try {
          const schema = JSON.parse(selectedTool.inputSchema);
          if (schema.required) {
            for (const field of schema.required) {
              if (!(field in input)) {
                throw new Error(
                  `Required field '${field}' is missing from input`,
                );
              }
            }
          }
        } catch (schemaError: any) {
          throw new Error(`Input validation failed: ${schemaError.message}`);
        }
      }

      // Use the tool engine for comprehensive testing
      const result = await toolEngine.testTool(selectedTool, input, sessionId);
      setTestResult(result);

      if (result.success) {
        toast({
          title: "Test Successful",
          description: `Tool executed successfully in ${result.executionTime}ms`,
        });

        // Validate output against schema if defined
        if (selectedTool.outputSchema && result.output) {
          try {
            const outputSchema = JSON.parse(selectedTool.outputSchema);
            // Basic output validation - in production, use proper JSON schema validator
            console.log("Output validation passed for:", outputSchema);
          } catch (error) {
            console.warn("Output schema validation failed:", error);
          }
        }
      } else {
        toast({
          title: "Test Failed",
          description: result.error || "Tool test failed",
          variant: "destructive",
        });
      }

      // Update session memory with comprehensive test result
      stateManager.addToMemory(sessionId, {
        type: "tool_test",
        toolId: selectedTool.id,
        toolName: selectedTool.name,
        input,
        result,
        timestamp: new Date(),
        metadata: {
          executionTime: result.executionTime,
          success: result.success,
          endpoint: selectedTool.endpoint,
          method: selectedTool.method,
          validationPassed: true,
        },
      });

      // Update tool analytics
      if (selectedTool.id) {
        loadToolAnalytics(selectedTool.id);
      }
    } catch (error: any) {
      console.error("Test failed:", error);
      const failedResult: ToolTestResult = {
        success: false,
        executionTime: 0,
        input: testInput,
        error: error.message,
        validationErrors: [error.message],
        performanceMetrics: {
          latency: 0,
          memoryUsage: 0,
          cpuUsage: 0,
        },
        timestamp: new Date(),
      };

      setTestResult(failedResult);

      toast({
        title: "Test Failed",
        description: error.message || "Tool test failed",
        variant: "destructive",
      });

      // Log failed test to session memory
      stateManager.addToMemory(sessionId, {
        type: "tool_test_failed",
        toolId: selectedTool.id,
        toolName: selectedTool.name,
        input: testInput,
        error: error.message,
        timestamp: new Date(),
      });
    } finally {
      setTesting(false);
    }
  };

  const handlePreviewExecution = async () => {
    if (!selectedTool) return;

    try {
      let input;
      try {
        input = JSON.parse(testInput);
      } catch (error) {
        throw new Error("Invalid JSON in test input");
      }

      const response = await apiService.previewToolExecution(
        selectedTool,
        input,
      );
      if (response.success) {
        toast({
          title: "Preview Generated",
          description: "Tool execution preview completed",
        });
      }
    } catch (error: any) {
      toast({
        title: "Preview Failed",
        description: error.message || "Failed to generate preview",
        variant: "destructive",
      });
    }
  };

  const handleExportTool = () => {
    if (!selectedTool) return;

    let exportData: string;
    let filename: string;
    let mimeType: string;

    switch (exportFormat) {
      case "json":
        exportData = JSON.stringify(selectedTool, null, 2);
        filename = `${selectedTool.name.replace(/\s+/g, "_")}.json`;
        mimeType = "application/json";
        break;
      case "yaml":
        // In production, you would use a YAML library
        exportData = JSON.stringify(selectedTool, null, 2);
        filename = `${selectedTool.name.replace(/\s+/g, "_")}.yaml`;
        mimeType = "text/yaml";
        break;
      case "openapi":
        // Generate OpenAPI spec
        const openApiSpec = {
          openapi: "3.0.0",
          info: {
            title: selectedTool.name,
            version: selectedTool.metadata?.version || "1.0.0",
          },
          paths: {
            [selectedTool.endpoint || "/"]: {
              [selectedTool.method.toLowerCase()]: {
                summary: selectedTool.name,
                description:
                  selectedTool?.description || "No description provided",
                requestBody: {
                  content: {
                    "application/json": {
                      schema: JSON.parse(selectedTool.inputSchema),
                    },
                  },
                },
                responses: {
                  "200": {
                    description: "Successful response",
                    content: {
                      "application/json": {
                        schema: JSON.parse(selectedTool.outputSchema),
                      },
                    },
                  },
                },
              },
            },
          },
        };
        exportData = JSON.stringify(openApiSpec, null, 2);
        filename = `${selectedTool.name.replace(/\s+/g, "_")}_openapi.json`;
        mimeType = "application/json";
        break;
      default:
        return;
    }

    const blob = new Blob([exportData], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export Successful",
      description: `Tool exported as ${filename}`,
    });
  };

  const updateConfig = (path: string, value: any) => {
    if (!selectedTool) return;

    const newConfig = { ...selectedTool };
    const pathParts = path.split(".");
    let current: any = newConfig;

    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {};
      }
      current = current[pathParts[i]];
    }

    current[pathParts[pathParts.length - 1]] = value;
    setSelectedTool(newConfig);
  };

  const filteredTools = useMemo(() => {
    return tools.filter(
      (tool) =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (tool.metadata?.tags || []).some((tag) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase()),
        ),
    );
  }, [tools, searchQuery]);

  const toolsByCategory = useMemo(() => {
    const categories: Record<string, ToolConfig[]> = {};
    filteredTools.forEach((tool) => {
      const category = tool.metadata?.category || "Uncategorized";
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(tool);
    });
    return categories;
  }, [filteredTools]);

  return (
    <div className="flex h-full bg-background">
      {/* Tool List Sidebar */}
      <div className="w-1/3 border-r p-4 flex flex-col h-full">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Tools</h2>
          <Button
            onClick={handleCreateNewTool}
            size="sm"
            className="flex items-center gap-1"
          >
            <PlusCircle className="h-4 w-4" />
            New Tool
          </Button>
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tools..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <ScrollArea className="flex-1">
          <div className="space-y-4">
            {Object.entries(toolsByCategory).map(
              ([category, categoryTools]) => (
                <div key={category}>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2 px-2">
                    {category}
                  </h3>
                  <div className="space-y-2">
                    {categoryTools.map((tool) => {
                      const toolAnalytics =
                        analytics && selectedTool?.id === tool.id
                          ? analytics
                          : null;

                      return (
                        <Card
                          key={tool.id || `temp-${tool.name}`}
                          className={`cursor-pointer hover:bg-accent/50 transition-colors ${
                            selectedTool?.id === tool.id
                              ? "border-primary bg-accent/30"
                              : ""
                          }`}
                          onClick={() => handleSelectTool(tool)}
                        >
                          <CardHeader className="p-4 pb-2">
                            <div className="flex justify-between items-start">
                              <CardTitle className="text-base">
                                {tool.name}
                              </CardTitle>
                              <div className="flex items-center gap-1">
                                {tool.endpoint && (
                                  <Badge
                                    variant="outline"
                                    className="text-xs bg-green-50 text-green-700 border-green-200"
                                  >
                                    <Globe className="h-3 w-3 mr-1" />
                                    API
                                  </Badge>
                                )}
                                {tool.hitl?.mode === "auto" && (
                                  <Badge
                                    variant="outline"
                                    className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                                  >
                                    HITL
                                  </Badge>
                                )}
                                {tool.hitl?.mode === "auto" && (
                                  <Badge
                                    variant="outline"
                                    className="text-xs bg-purple-50 text-purple-700 border-purple-200"
                                  >
                                    <Shield className="h-3 w-3 mr-1" />
                                    Isolated
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <CardDescription className="line-clamp-2 text-xs">
                              {tool.description || "No description provided"}
                            </CardDescription>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {tool.method || "POST"}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                <Clock className="h-3 w-3 mr-1" />
                                {tool.timeout || 30000}ms
                              </Badge>
                              {toolAnalytics && (
                                <Badge variant="outline" className="text-xs">
                                  <Activity className="h-3 w-3 mr-1" />
                                  {(
                                    (1 - toolAnalytics.errorRate) *
                                    100
                                  ).toFixed(1)}
                                  %
                                </Badge>
                              )}
                            </div>
                            {tool.metadata?.tags &&
                              tool.metadata?.tags?.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {tool.metadata?.tags
                                    ?.slice(0, 3)
                                    .map((tag, index) => (
                                      <Badge
                                        key={index}
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {tag}
                                      </Badge>
                                    ))}
                                  {tool.metadata?.tags?.length > 3 && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      +{tool.metadata?.tags?.length - 3}
                                    </Badge>
                                  )}
                                </div>
                              )}
                          </CardHeader>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              ),
            )}

            {filteredTools.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Code className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No tools found</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Tool Configuration Panel */}
      <div className="flex-1 p-4 overflow-auto">
        {selectedTool ? (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-2xl font-semibold">
                  {isEditing ? (
                    <Input
                      value={selectedTool.name}
                      onChange={(e) => updateConfig("name", e.target.value)}
                      className="text-2xl font-semibold h-auto py-1 px-2"
                    />
                  ) : (
                    selectedTool.name
                  )}
                </h2>
                {validationErrors.length > 0 && (
                  <Badge
                    variant="destructive"
                    className="flex items-center gap-1"
                  >
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.length} Error
                    {validationErrors.length !== 1 ? "s" : ""}
                  </Badge>
                )}
              </div>

              <div className="flex gap-2">
                {!isEditing && (
                  <>
                    <Button
                      onClick={() => setPreviewMode(!previewMode)}
                      variant="outline"
                      size="sm"
                    >
                      {previewMode ? (
                        <EyeOff className="h-4 w-4 mr-2" />
                      ) : (
                        <Eye className="h-4 w-4 mr-2" />
                      )}
                      {previewMode ? "Hide Preview" : "Preview"}
                    </Button>
                    <Button
                      onClick={handleTestTool}
                      variant="outline"
                      disabled={testing || validationErrors.length > 0}
                    >
                      {testing ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Play className="h-4 w-4 mr-2" />
                      )}
                      {testing ? "Testing..." : "Test"}
                    </Button>
                  </>
                )}

                {isEditing ? (
                  <>
                    <Button
                      onClick={handleSaveTool}
                      variant="default"
                      disabled={loading || validationErrors.length > 0}
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Check className="h-4 w-4 mr-2" />
                      )}
                      {loading ? "Saving..." : "Save"}
                    </Button>
                    <Button
                      onClick={() => setIsEditing(false)}
                      variant="outline"
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={() => setIsEditing(true)}
                      variant="outline"
                      disabled={loading}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      onClick={handleExportTool}
                      variant="outline"
                      disabled={loading}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button
                      onClick={handleDeleteTool}
                      variant="destructive"
                      disabled={loading || !selectedTool?.id}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Configuration Errors</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1 mt-2">
                    {validationErrors.map((error, index) => (
                      <li key={index} className="text-sm">
                        {error}
                      </li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Main Content */}
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-6 mb-6">
                <TabsTrigger value="configuration">Configuration</TabsTrigger>
                <TabsTrigger value="schemas">Schemas</TabsTrigger>
                <TabsTrigger value="testing">Testing</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="execution">Execution</TabsTrigger>
                <TabsTrigger value="events">Events</TabsTrigger>
              </TabsList>

              <TabsContent value="configuration" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Configuration</CardTitle>
                    <CardDescription>
                      Configure the basic properties and behavior of your tool
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Description</Label>
                      {isEditing ? (
                        <Textarea
                          value={selectedTool.description}
                          onChange={(e) =>
                            updateConfig("description", e.target.value)
                          }
                          className="mt-1"
                          rows={3}
                          placeholder="Describe what this tool does and how it should be used"
                        />
                      ) : (
                        <p className="mt-1 text-muted-foreground">
                          {selectedTool.description ||
                            "No description provided"}
                        </p>
                      )}
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="endpoint">API Endpoint</Label>
                        {isEditing ? (
                          <Input
                            id="endpoint"
                            value={selectedTool.endpoint || ""}
                            onChange={(e) =>
                              updateConfig("endpoint", e.target.value)
                            }
                            placeholder="https://api.example.com/endpoint"
                          />
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            {selectedTool.endpoint || "No endpoint configured"}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="method">HTTP Method</Label>
                        {isEditing ? (
                          <Select
                            value={selectedTool.method}
                            onValueChange={(value: any) =>
                              updateConfig("method", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="GET">GET</SelectItem>
                              <SelectItem value="POST">POST</SelectItem>
                              <SelectItem value="PUT">PUT</SelectItem>
                              <SelectItem value="DELETE">DELETE</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            {selectedTool.method}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="timeout">Timeout (ms)</Label>
                        {isEditing ? (
                          <Input
                            id="timeout"
                            type="number"
                            value={selectedTool.timeout}
                            onChange={(e) =>
                              updateConfig("timeout", parseInt(e.target.value))
                            }
                          />
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            {selectedTool.timeout}ms
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="retryAttempts">Retry Attempts</Label>
                        {isEditing ? (
                          <Input
                            id="retryAttempts"
                            type="number"
                            value={selectedTool.retryAttempts}
                            onChange={(e) =>
                              updateConfig(
                                "retryAttempts",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            {selectedTool.retryAttempts} attempts
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="fallbackStrategy">
                          Fallback Strategy
                        </Label>
                        {isEditing ? (
                          <Select
                            value={selectedTool.hitl?.fallbackStrategy}
                            onValueChange={(value: any) =>
                              updateConfig("fallbackStrategy", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              <SelectItem value="default_value">
                                Default Value
                              </SelectItem>
                              <SelectItem value="alternative_endpoint">
                                Alternative Endpoint
                              </SelectItem>
                              <SelectItem value="cached_response">
                                Cached Response
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-sm text-muted-foreground capitalize">
                            {selectedTool.hitl?.fallbackStrategy?.replace(
                              "_",
                              " ",
                            ) || "None"}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Advanced Settings */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Advanced Settings</Label>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            setShowAdvancedSettings(!showAdvancedSettings)
                          }
                        >
                          {showAdvancedSettings ? "Hide" : "Show"} Advanced
                        </Button>
                      </div>

                      {showAdvancedSettings && (
                        <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                          <div className="space-y-4">
                            <h4 className="font-medium">Parameter Binding</h4>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="flex items-center justify-between">
                                <Label htmlFor="sessionContext">
                                  Session Context
                                </Label>
                                {isEditing ? (
                                  <Switch
                                    id="sessionContext"
                                    checked={
                                      selectedTool.hitl?.parameterBinding
                                        ?.sessionContext || false
                                    }
                                    onCheckedChange={(checked) =>
                                      updateConfig(
                                        "hitl.parameterBinding.sessionContext",
                                        checked,
                                      )
                                    }
                                  />
                                ) : (
                                  <Badge
                                    variant={
                                      selectedTool.hitl?.parameterBinding
                                        ?.sessionContext
                                        ? "default"
                                        : "secondary"
                                    }
                                  >
                                    {selectedTool.hitl?.parameterBinding
                                      ?.sessionContext
                                      ? "Enabled"
                                      : "Disabled"}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center justify-between">
                                <Label htmlFor="userContext">
                                  User Context
                                </Label>
                                {isEditing ? (
                                  <Switch
                                    id="userContext"
                                    checked={
                                      selectedTool.hitl?.parameterBinding
                                        ?.userContext || false
                                    }
                                    onCheckedChange={(checked) =>
                                      updateConfig(
                                        "hitl.parameterBinding.userContext",
                                        checked,
                                      )
                                    }
                                  />
                                ) : (
                                  <Badge
                                    variant={
                                      selectedTool.hitl?.parameterBinding
                                        ?.userContext
                                        ? "default"
                                        : "secondary"
                                    }
                                  >
                                    {selectedTool.hitl?.parameterBinding
                                      ?.userContext
                                      ? "Enabled"
                                      : "Disabled"}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center justify-between">
                                <Label htmlFor="agentMemory">
                                  Agent Memory
                                </Label>
                                {isEditing ? (
                                  <Switch
                                    id="agentMemory"
                                    checked={
                                      selectedTool.hitl?.parameterBinding
                                        ?.agentMemory || false
                                    }
                                    onCheckedChange={(checked) =>
                                      updateConfig(
                                        "hitl.parameterBinding.agentMemory",
                                        checked,
                                      )
                                    }
                                  />
                                ) : (
                                  <Badge
                                    variant={
                                      selectedTool.hitl?.parameterBinding
                                        ?.agentMemory
                                        ? "default"
                                        : "secondary"
                                    }
                                  >
                                    {selectedTool.hitl?.parameterBinding
                                      ?.agentMemory
                                      ? "Enabled"
                                      : "Disabled"}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>

                          <Separator />

                          {/* Remove the Execution Context section since it's not in the ToolConfig type */}
                          <div className="space-y-4">
                            <h4 className="font-medium">
                              Analytics & Monitoring
                            </h4>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="flex items-center justify-between">
                                <Label htmlFor="trackUsage">Track Usage</Label>
                                {isEditing ? (
                                  <Switch
                                    id="trackUsage"
                                    checked={false}
                                    onCheckedChange={(checked) =>
                                      updateConfig(
                                        "metadata.trackUsage",
                                        checked,
                                      )
                                    }
                                  />
                                ) : (
                                  <Badge variant="secondary">Disabled</Badge>
                                )}
                              </div>
                              <div className="flex items-center justify-between">
                                <Label htmlFor="trackPerformance">
                                  Track Performance
                                </Label>
                                {isEditing ? (
                                  <Switch
                                    id="trackPerformance"
                                    checked={false}
                                    onCheckedChange={(checked) =>
                                      updateConfig(
                                        "metadata.trackPerformance",
                                        checked,
                                      )
                                    }
                                  />
                                ) : (
                                  <Badge variant="secondary">Disabled</Badge>
                                )}
                              </div>
                              <div className="flex items-center justify-between">
                                <Label htmlFor="trackErrors">
                                  Track Errors
                                </Label>
                                {isEditing ? (
                                  <Switch
                                    id="trackErrors"
                                    checked={false}
                                    onCheckedChange={(checked) =>
                                      updateConfig(
                                        "metadata.trackErrors",
                                        checked,
                                      )
                                    }
                                  />
                                ) : (
                                  <Badge variant="secondary">Disabled</Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="schemas" className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base flex items-center gap-2">
                          <Code2 className="h-4 w-4" />
                          Input Schema
                          {schemaValidation.input ? (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                        </CardTitle>
                        {isEditing && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const sampleSchema = {
                                type: "object",
                                properties: {
                                  input: {
                                    type: "string",
                                    description: "Input parameter",
                                    example: "Hello World",
                                  },
                                },
                                required: ["input"],
                              };
                              updateConfig(
                                "inputSchema",
                                JSON.stringify(sampleSchema, null, 2),
                              );
                            }}
                          >
                            <Braces className="h-4 w-4 mr-1" />
                            Sample
                          </Button>
                        )}
                      </div>
                      <CardDescription>
                        Define the expected input parameters using JSON Schema
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isEditing ? (
                        <Textarea
                          value={selectedTool.inputSchema}
                          onChange={(e) =>
                            updateConfig("inputSchema", e.target.value)
                          }
                          className="font-mono text-sm"
                          rows={15}
                        />
                      ) : (
                        <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[400px] text-sm">
                          {JSON.stringify(
                            JSON.parse(selectedTool.inputSchema),
                            null,
                            2,
                          )}
                        </pre>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base flex items-center gap-2">
                          <Code2 className="h-4 w-4" />
                          Output Schema
                          {schemaValidation.output ? (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                        </CardTitle>
                        {isEditing && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const sampleSchema = {
                                type: "object",
                                properties: {
                                  result: {
                                    type: "string",
                                    description: "Output result",
                                  },
                                  timestamp: {
                                    type: "string",
                                    format: "date-time",
                                    description: "Execution timestamp",
                                  },
                                },
                              };
                              updateConfig(
                                "outputSchema",
                                JSON.stringify(sampleSchema, null, 2),
                              );
                            }}
                          >
                            <Braces className="h-4 w-4 mr-1" />
                            Sample
                          </Button>
                        )}
                      </div>
                      <CardDescription>
                        Define the expected output format using JSON Schema
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isEditing ? (
                        <Textarea
                          value={selectedTool.outputSchema}
                          onChange={(e) =>
                            updateConfig("outputSchema", e.target.value)
                          }
                          className="font-mono text-sm"
                          rows={15}
                        />
                      ) : (
                        <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[400px] text-sm">
                          {JSON.stringify(
                            JSON.parse(selectedTool.outputSchema),
                            null,
                            2,
                          )}
                        </pre>
                      )}
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">
                      Validation Rules
                    </CardTitle>
                    <CardDescription>
                      Define additional validation rules and constraints
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isEditing ? (
                      <Textarea
                        value={selectedTool.validationRules}
                        onChange={(e) =>
                          updateConfig("validationRules", e.target.value)
                        }
                        className="text-sm"
                        rows={6}
                        placeholder="Enter custom validation rules or constraints"
                      />
                    ) : (
                      <div className="bg-muted p-4 rounded-md">
                        {selectedTool.validationRules ||
                          "No validation rules defined"}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="testing" className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <TestTube className="h-4 w-4" />
                        Test Harness
                      </CardTitle>
                      <CardDescription>
                        Test your tool with custom input data
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label>Test Input (JSON)</Label>
                        <Textarea
                          value={testInput}
                          onChange={(e) => setTestInput(e.target.value)}
                          className="font-mono text-sm"
                          rows={10}
                          placeholder="Enter test input as JSON"
                        />
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={handleTestTool}
                          disabled={testing || !selectedTool.endpoint}
                          className="flex-1"
                        >
                          {testing ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Play className="h-4 w-4 mr-2" />
                          )}
                          {testing ? "Testing..." : "Run Test"}
                        </Button>
                        <Button
                          onClick={handlePreviewExecution}
                          variant="outline"
                          disabled={!selectedTool.endpoint}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <Terminal className="h-4 w-4" />
                        Test Results
                      </CardTitle>
                      <CardDescription>
                        View the results of your tool execution
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {testResult ? (
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            {testResult.success ? (
                              <Badge
                                variant="outline"
                                className="bg-green-50 text-green-700 border-green-200"
                              >
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Success
                              </Badge>
                            ) : (
                              <Badge variant="destructive">
                                <XCircle className="h-3 w-3 mr-1" />
                                Failed
                              </Badge>
                            )}
                            <Badge variant="outline">
                              <Timer className="h-3 w-3 mr-1" />
                              {testResult.executionTime}ms
                            </Badge>
                          </div>

                          <div className="space-y-2">
                            <Label>Performance Metrics</Label>
                            <div className="grid grid-cols-3 gap-2 text-xs">
                              <div className="flex items-center gap-1">
                                <Timer className="h-3 w-3" />
                                Latency: {testResult.performanceMetrics.latency}
                                ms
                              </div>
                              <div className="flex items-center gap-1">
                                <HardDrive className="h-3 w-3" />
                                Memory:{" "}
                                {testResult.performanceMetrics.memoryUsage}MB
                              </div>
                              <div className="flex items-center gap-1">
                                <Cpu className="h-3 w-3" />
                                CPU: {testResult.performanceMetrics.cpuUsage}%
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Output</Label>
                            <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-[200px]">
                              {testResult.success
                                ? JSON.stringify(testResult.output, null, 2)
                                : testResult.error}
                            </pre>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <TestTube className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No test results yet</p>
                          <p className="text-xs">
                            Run a test to see results here
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-6">
                {analytics ? (
                  <div className="grid grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <BarChart3 className="h-4 w-4" />
                          Usage Statistics
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold">
                              {analytics.usageCount}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Total Calls
                            </div>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">
                              {analytics.successCount}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Successful
                            </div>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-red-600">
                              {analytics.failureCount}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Failed
                            </div>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold">
                              {(analytics.errorRate * 100).toFixed(1)}%
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Error Rate
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <Activity className="h-4 w-4" />
                          Performance Metrics
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Average Latency</span>
                            <span className="font-medium">
                              {analytics.averageLatency}ms
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">P50 Latency</span>
                            <span className="font-medium">
                              {analytics.p50Latency}ms
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">P95 Latency</span>
                            <span className="font-medium">
                              {analytics.p95Latency}ms
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">P99 Latency</span>
                            <span className="font-medium">
                              {analytics.p99Latency}ms
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Throughput</span>
                            <span className="font-medium">
                              {analytics.throughput} req/s
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  <Card>
                    <CardContent className="text-center py-8">
                      <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm text-muted-foreground">
                        No analytics data available
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Execute the tool to generate analytics
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="execution" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Play className="h-4 w-4" />
                      Execution History
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Label>Total Executions</Label>
                        <span className="font-medium">
                          {executionHistory.length}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label>Average Execution Time</Label>
                        <span className="font-medium">
                          {executionHistory.length > 0
                            ? (
                                executionHistory.reduce(
                                  (sum, item) =>
                                    sum + (item.metadata.executionTime || 0),
                                  0,
                                ) / executionHistory.length
                              ).toFixed(0)
                            : 0}
                          ms
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <div className="flex justify-end gap-2 mt-4 md:mt-0">
              <Button onClick={handleSaveTool}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>

              <Button onClick={handleDeleteTool} variant="destructive">
                <Trash className="h-4 w-4 mr-2" />
                <span className="hidden md:block">Delete Tool</span>
                <span className="block md:hidden">Del</span>
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Code className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No tool selected</p>
            <p className="text-xs">
              Select a tool from the sidebar or create a new one
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolManager;
