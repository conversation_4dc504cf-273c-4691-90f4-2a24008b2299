import {
  AgentConfig,
  SessionState,
  ProviderR<PERSON>ponse,
  HITLRequest,
} from "@/types/api";
import { wsService } from "./websocket";
import { stateManager } from "./state";
import { apiService } from "./api";
import { z } from "zod";

// Production-grade constants for agent execution
const AGENT_CONSTANTS = {
  MAX_CONCURRENT_AGENTS: 25,
  DEFAULT_TIMEOUT: 45000,
  MAX_TIMEOUT: 300000,
  MIN_TIMEOUT: 5000,
  MAX_REASONING_STEPS: 10,
  MAX_CONTEXT_SIZE: 50000,
  DEFAULT_CONFIDENCE_THRESHOLD: 0.7,
  MAX_RETRY_ATTEMPTS: 3,
  MEMORY_LIMIT: 1000,
  TOKEN_BUFFER: 500,
} as const;

// Enhanced error types for agent execution
class AgentExecutionError extends Error {
  constructor(
    message: string,
    public code: string,
    public category:
      | "validation"
      | "execution"
      | "timeout"
      | "resource"
      | "provider"
      | "reasoning"
      | "memory",
    public retryable: boolean = false,
    public details?: Record<string, any>,
  ) {
    super(message);
    this.name = "AgentExecutionError";
  }
}

// Agent execution context
interface AgentExecutionContext {
  id: string;
  agentId: string;
  sessionId: string;
  correlationId?: string;
  startTime: Date;
  endTime?: Date;
  status: "pending" | "running" | "completed" | "failed" | "timeout" | "cancelled";
  input: any;
  output?: any;
  error?: string;
  errorCode?: string;
  stackTrace?: string;
  reasoning: {
    steps: Array<{
      step: number;
      thought: string;
      action?: string;
      observation?: string;
      timestamp: Date;
      confidence?: number;
    }>;
    finalDecision: string;
    confidence: number;
    tokensUsed: number;
    providersUsed: string[];
  };
  memory: {
    accessed: any[];
    updated: any[];
    contextSize: number;
  };
  performance: {
    executionTime: number;
    reasoningTime: number;
    memoryAccessTime: number;
    providerLatency: number;
    tokensPerSecond: number;
  };
  events: Array<{
    id: string;
    timestamp: Date;
    type: string;
    message: string;
    data?: any;
    severity: "info" | "warning" | "error" | "critical";
  }>;
}

// Provider interface for AI model communication
class ProviderInterface {
  private providers: Map<string, any> = new Map();
  private providerMetrics: Map<string, {
    latency: number;
    successRate: number;
    errorRate: number;
    lastUsed: Date;
    tokensUsed: number;
  }> = new Map();

  async executeWithProvider(
    providerId: string,
    agentConfig: AgentConfig,
    input: any,
    context: any,
    sessionState?: SessionState,
  ): Promise<{
    output: any;
    reasoning: any[];
    tokensUsed: number;
    confidence: number;
    executionTime: number;
  }> {
    const startTime = Date.now();
    
    try {
      // Get provider configuration
      const providerResponse = await apiService.request(`/providers/${providerId}`);
      if (!providerResponse.success) {
        throw new AgentExecutionError(
          `Provider ${providerId} not found`,
          "PROVIDER_NOT_FOUND",
          "provider"
        );
      }

      const provider = providerResponse.data;
      
      // Build prompt based on agent configuration
      const prompt = this.buildPrompt(agentConfig, input, context, sessionState);
      
      // Execute with the specific provider
      const response = await this.callProvider(provider, prompt, agentConfig);
      
      const executionTime = Date.now() - startTime;
      
      // Update provider metrics
      this.updateProviderMetrics(providerId, true, executionTime, response.tokensUsed || 0);
      
      return {
        output: response.content,
        reasoning: response.reasoning || [],
        tokensUsed: response.tokensUsed || 0,
        confidence: response.confidence || 0.8,
        executionTime,
      };
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      this.updateProviderMetrics(providerId, false, executionTime, 0);
      throw error;
    }
  }

  private buildPrompt(
    agentConfig: AgentConfig,
    input: any,
    context: any,
    sessionState?: SessionState,
  ): string {
    let prompt = "";
    
    // Add system prompt based on agent description
    if (agentConfig.description) {
      prompt += `System: ${agentConfig.description}\n\n`;
    }
    
    // Add reasoning instructions if enabled
    if (agentConfig.reasoning.enabled) {
      prompt += `Instructions: Use ${agentConfig.reasoning.type} reasoning with ${agentConfig.reasoning.steps} steps. `;
      prompt += "Think step by step and show your reasoning process.\n\n";
    }
    
    // Add memory context if available
    if (agentConfig.memory.enabled && sessionState?.memory.length > 0) {
      const recentMemory = sessionState.memory.slice(-agentConfig.memory.contextWindow);
      prompt += "Previous context:\n";
      recentMemory.forEach((item, index) => {
        prompt += `${index + 1}. ${JSON.stringify(item)}\n`;
      });
      prompt += "\n";
    }
    
    // Add current context
    if (Object.keys(context).length > 0) {
      prompt += `Current context: ${JSON.stringify(context)}\n\n`;
    }
    
    // Add the actual input
    prompt += `User input: ${typeof input === 'string' ? input : JSON.stringify(input)}\n\n`;
    
    // Add response format instructions
    prompt += "Please provide a structured response with your reasoning and final answer.";
    
    return prompt;
  }

  private async callProvider(provider: any, prompt: string, agentConfig: AgentConfig): Promise<{
    content: any;
    reasoning?: any[];
    tokensUsed?: number;
    confidence?: number;
  }> {
    const requestBody = {
      model: agentConfig.model,
      messages: [{ role: "user", content: prompt }],
      temperature: agentConfig.temperature,
      max_tokens: agentConfig.maxTokens,
      stream: false,
    };

    // Make API call to the provider
    const response = await fetch(provider.baseUrl + "/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${provider.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new AgentExecutionError(
        `Provider API error: ${response.status} ${response.statusText}`,
        "PROVIDER_API_ERROR",
        "provider",
        true
      );
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || "",
      tokensUsed: data.usage?.total_tokens || 0,
      confidence: 0.8, // Default confidence
    };
  }

  private updateProviderMetrics(
    providerId: string,
    success: boolean,
    executionTime: number,
    tokensUsed: number,
  ) {
    const current = this.providerMetrics.get(providerId) || {
      latency: 0,
      successRate: 0,
      errorRate: 0,
      lastUsed: new Date(),
      tokensUsed: 0,
    };

    // Update metrics with exponential moving average
    current.latency = current.latency * 0.8 + executionTime * 0.2;
    current.successRate = success 
      ? current.successRate * 0.9 + 0.1
      : current.successRate * 0.9;
    current.errorRate = !success 
      ? current.errorRate * 0.9 + 0.1
      : current.errorRate * 0.9;
    current.lastUsed = new Date();
    current.tokensUsed += tokensUsed;

    this.providerMetrics.set(providerId, current);
  }

  getProviderMetrics(providerId: string) {
    return this.providerMetrics.get(providerId);
  }
}

// Main Agent Engine
class AgentEngine {
  private executionContexts: Map<string, AgentExecutionContext> = new Map();
  private activeExecutions: Set<string> = new Set();
  private providerInterface: ProviderInterface = new ProviderInterface();
  private eventHandlers: Map<string, ((event: any) => void)[]> = new Map();
  private hitlQueue: Map<string, HITLRequest> = new Map();

  constructor() {
    this.setupEventListeners();
    this.startPeriodicCleanup();
  }

  private setupEventListeners() {
    wsService.on("agent:execution_update", (data) => {
      this.handleExecutionUpdate(data);
    });

    wsService.on("agent:cancel_request", (data) => {
      this.handleCancelRequest(data);
    });

    wsService.on("hitl:response", (data) => {
      this.handleHITLResponse(data);
    });
  }

  private handleExecutionUpdate(data: {
    executionId: string;
    update: Partial<AgentExecutionContext>;
  }) {
    const context = this.executionContexts.get(data.executionId);
    if (context) {
      Object.assign(context, data.update);
      this.emitEvent("agent:execution_updated", {
        executionId: data.executionId,
        context,
      });
    }
  }

  private handleCancelRequest(data: { executionId: string }) {
    this.cancelExecution(data.executionId);
  }

  private handleHITLResponse(data: { requestId: string; response: any }) {
    const request = this.hitlQueue.get(data.requestId);
    if (request) {
      this.hitlQueue.delete(data.requestId);
      this.emitEvent("hitl:resolved", { requestId: data.requestId, response: data.response });
    }
  }

  public async executeAgent(
    agentConfig: AgentConfig,
    input: any,
    sessionId: string,
    context: Record<string, any> = {},
  ): Promise<AgentExecutionContext> {
    // Validate input size
    const inputSize = JSON.stringify(input).length;
    if (inputSize > AGENT_CONSTANTS.MAX_CONTEXT_SIZE) {
      throw new AgentExecutionError(
        `Input size ${inputSize} bytes exceeds maximum ${AGENT_CONSTANTS.MAX_CONTEXT_SIZE} bytes`,
        "INPUT_TOO_LARGE",
        "validation"
      );
    }

    // Check concurrent execution limits
    if (this.activeExecutions.size >= AGENT_CONSTANTS.MAX_CONCURRENT_AGENTS) {
      throw new AgentExecutionError(
        "Maximum concurrent agent executions reached",
        "CONCURRENT_LIMIT_EXCEEDED",
        "resource"
      );
    }

    const executionId = this.generateExecutionId();
    const correlationId = this.generateCorrelationId();

    // Initialize execution context
    const executionContext: AgentExecutionContext = {
      id: executionId,
      agentId: agentConfig.id || "temp",
      sessionId,
      correlationId,
      startTime: new Date(),
      status: "pending",
      input,
      reasoning: {
        steps: [],
        finalDecision: "",
        confidence: 0,
        tokensUsed: 0,
        providersUsed: [],
      },
      memory: {
        accessed: [],
        updated: [],
        contextSize: 0,
      },
      performance: {
        executionTime: 0,
        reasoningTime: 0,
        memoryAccessTime: 0,
        providerLatency: 0,
        tokensPerSecond: 0,
      },
      events: [],
    };

    this.executionContexts.set(executionId, executionContext);
    this.activeExecutions.add(executionId);

    try {
      this.addEvent(
        executionContext,
        "execution_started",
        "Agent execution initiated",
        "info"
      );

      // Update status to running
      executionContext.status = "running";
      this.emitEvent("agent:execution_started", {
        executionId,
        agentId: agentConfig.id,
        sessionId,
      });

      // Get session state for memory access
      const sessionState = stateManager.getState(sessionId);
      
      // Execute with provider selection and fallback
      const result = await this.executeWithProviderFallback(
        agentConfig,
        input,
        context,
        sessionState,
        executionContext
      );

      // Update execution context with results
      executionContext.status = "completed";
      executionContext.output = result.output;
      executionContext.reasoning = {
        steps: result.reasoning || [],
        finalDecision: result.output?.summary || "Execution completed",
        confidence: result.confidence,
        tokensUsed: result.tokensUsed,
        providersUsed: result.providersUsed || [],
      };
      executionContext.endTime = new Date();
      executionContext.performance.executionTime = 
        executionContext.endTime.getTime() - executionContext.startTime.getTime();
      
      if (result.tokensUsed > 0 && executionContext.performance.executionTime > 0) {
        executionContext.performance.tokensPerSecond = 
          result.tokensUsed / (executionContext.performance.executionTime / 1000);
      }

      this.addEvent(
        executionContext,
        "execution_completed",
        `Agent execution completed successfully in ${executionContext.performance.executionTime}ms`,
        "info"
      );

      // Update session memory if enabled
      if (agentConfig.memory.enabled && result.output) {
        stateManager.addToMemory(sessionId, {
          type: "agent_response",
          input,
          output: result.output,
          timestamp: new Date(),
          agentId: agentConfig.id,
          confidence: result.confidence,
        });
      }

      // Emit completion event
      this.emitEvent("agent:execution_completed", {
        executionId,
        result: result.output,
        performance: executionContext.performance,
      });

      return executionContext;
    } catch (error: any) {
      // Handle execution error
      executionContext.status = "failed";
      executionContext.error = error.message;
      executionContext.errorCode = error.code || "UNKNOWN_ERROR";
      executionContext.stackTrace = error.stack;
      executionContext.endTime = new Date();
      executionContext.performance.executionTime = 
        executionContext.endTime.getTime() - executionContext.startTime.getTime();

      this.addEvent(
        executionContext,
        "execution_failed",
        `Agent execution failed: ${error.message}`,
        "error"
      );

      // Emit error event
      this.emitEvent("agent:execution_failed", {
        executionId,
        error: error.message,
        errorCode: executionContext.errorCode,
      });

      return executionContext;
    } finally {
      // Always clean up
      this.activeExecutions.delete(executionId);
    }
  }

  private async executeWithProviderFallback(
    agentConfig: AgentConfig,
    input: any,
    context: any,
    sessionState: SessionState | null,
    executionContext: AgentExecutionContext
  ): Promise<{
    output: any;
    reasoning?: any[];
    tokensUsed: number;
    confidence: number;
    providersUsed: string[];
  }> {
    const providers = agentConfig.providers.length > 0 
      ? agentConfig.providers 
      : ['default']; // Use default provider if none specified
    
    let lastError: Error;
    const providersUsed: string[] = [];
    
    for (const providerId of providers) {
      try {
        this.addEvent(
          executionContext,
          "provider_attempt",
          `Attempting execution with provider: ${providerId}`,
          "info"
        );
        
        const result = await this.providerInterface.executeWithProvider(
          providerId,
          agentConfig,
          input,
          context,
          sessionState
        );
        
        providersUsed.push(providerId);
        
        this.addEvent(
          executionContext,
          "provider_success",
          `Successfully executed with provider: ${providerId}`,
          "info"
        );
        
        return {
          ...result,
          providersUsed,
        };
      } catch (error: any) {
        lastError = error;
        providersUsed.push(providerId);
        
        this.addEvent(
          executionContext,
          "provider_failed",
          `Provider ${providerId} failed: ${error.message}`,
          "warning"
        );
        
        // Continue to next provider if this one fails
        continue;
      }
    }
    
    // All providers failed
    throw new AgentExecutionError(
      `All providers failed. Last error: ${lastError!.message}`,
      "ALL_PROVIDERS_FAILED",
      "provider",
      false,
      { providersUsed, lastError: lastError!.message }
    );
  }

  public async cancelExecution(executionId: string): Promise<boolean> {
    const context = this.executionContexts.get(executionId);
    if (
      !context ||
      context.status === "completed" ||
      context.status === "failed"
    ) {
      return false;
    }

    context.status = "cancelled";
    context.endTime = new Date();
    context.performance.executionTime = 
      context.endTime.getTime() - context.startTime.getTime();

    this.addEvent(
      context,
      "execution_cancelled",
      "Execution cancelled by user request",
      "warning"
    );

    this.activeExecutions.delete(executionId);

    this.emitEvent("agent:execution_cancelled", { executionId });

    return true;
  }

  public getExecutionContext(
    executionId: string,
  ): AgentExecutionContext | undefined {
    return this.executionContexts.get(executionId);
  }

  public onEvent(eventType: string, handler: (event: any) => void) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  public offEvent(eventType: string, handler: (event: any) => void) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emitEvent(eventType: string, data: any) {
    const handlers = this.eventHandlers.get(eventType) || [];
    handlers.forEach((handler) => {
      try {
        handler(data);
      } catch (error) {
        console.error("Error in agent event handler:", error);
      }
    });

    // Also emit via WebSocket
    wsService.send(eventType, data);
  }

  private addEvent(
    context: AgentExecutionContext,
    type: string,
    message: string,
    severity: "info" | "warning" | "error" | "critical" = "info",
  ) {
    const event = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type,
      message,
      severity,
    };

    context.events.push(event);

    // Keep only last 50 events per execution
    if (context.events.length > 50) {
      context.events = context.events.slice(-50);
    }
  }

  private generateExecutionId(): string {
    return `agent_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCorrelationId(): string {
    return `agent_corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `agent_evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private startPeriodicCleanup() {
    // Clean up old execution contexts every 10 minutes
    setInterval(
      () => {
        this.cleanup();
      },
      10 * 60 * 1000,
    );
  }

  public cleanup() {
    // Clean up old execution contexts (older than 2 hours)
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);

    for (const [executionId, context] of this.executionContexts.entries()) {
      if (context.startTime < twoHoursAgo) {
        this.executionContexts.delete(executionId);
      }
    }
  }

  public getExecutionMetrics(): {
    activeExecutions: number;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
  } {
    const totalExecutions = this.executionContexts.size;
    let successfulExecutions = 0;
    let failedExecutions = 0;

    for (const context of this.executionContexts.values()) {
      if (context.status === "completed") {
        successfulExecutions++;
      } else if (context.status === "failed") {
        failedExecutions++;
      }
    }

    return {
      activeExecutions: this.activeExecutions.size,
      totalExecutions,
      successfulExecutions,
      failedExecutions,
    };
  }
}

export const agentEngine = new AgentEngine();

// Cleanup old contexts every hour
setInterval(
  () => {
    agentEngine.cleanup();
  },
  60 * 60 * 1000,
);
