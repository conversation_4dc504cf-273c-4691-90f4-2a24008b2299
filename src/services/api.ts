import {
  AIProvider,
  AgentConfig,
  ToolConfig,
  HITLRequest,
  ProviderResponse,
  APIResponse,
  ToolAgentHybridConfig,
  HybridExecutionContext,
  HybridAnalytics,
} from "@/types/api";

const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:3001/api";

class APIService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<APIResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "API request failed");
      }

      return data;
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  // Provider Management
  async getProviders(): Promise<APIResponse<AIProvider[]>> {
    return this.request("/providers");
  }

  async updateProvider(
    id: string,
    provider: Partial<AIProvider>,
  ): Promise<APIResponse<AIProvider>> {
    return this.request(`/providers/${id}`, {
      method: "PUT",
      body: JSON.stringify(provider),
    });
  }

  async testProviderConnection(
    id: string,
  ): Promise<APIResponse<{ connected: boolean; latency: number }>> {
    return this.request(`/providers/${id}/test`, {
      method: "POST",
    });
  }

  async updateProviderPriorities(
    priorities: { id: string; priority: number }[],
  ): Promise<APIResponse<void>> {
    return this.request("/providers/priorities", {
      method: "PUT",
      body: JSON.stringify({ priorities }),
    });
  }

  // Agent Management
  async getAgents(): Promise<APIResponse<AgentConfig[]>> {
    return this.request("/agents");
  }

  async createAgent(agent: AgentConfig): Promise<APIResponse<AgentConfig>> {
    return this.request("/agents", {
      method: "POST",
      body: JSON.stringify(agent),
    });
  }

  async updateAgent(
    id: string,
    agent: Partial<AgentConfig>,
  ): Promise<APIResponse<AgentConfig>> {
    return this.request(`/agents/${id}`, {
      method: "PUT",
      body: JSON.stringify(agent),
    });
  }

  async deleteAgent(id: string): Promise<APIResponse<void>> {
    return this.request(`/agents/${id}`, {
      method: "DELETE",
    });
  }

  async testAgent(id: string, input: any): Promise<APIResponse<any>> {
    return this.request(`/agents/${id}/test`, {
      method: "POST",
      body: JSON.stringify({ input }),
    });
  }

  async deployAgent(
    id: string,
  ): Promise<APIResponse<{ deploymentId: string; endpoint: string }>> {
    return this.request(`/agents/${id}/deploy`, {
      method: "POST",
    });
  }

  // Tool Management
  async getTools(): Promise<APIResponse<ToolConfig[]>> {
    return this.request("/tools");
  }

  async createTool(tool: ToolConfig): Promise<APIResponse<ToolConfig>> {
    return this.request("/tools", {
      method: "POST",
      body: JSON.stringify(tool),
    });
  }

  async updateTool(
    id: string,
    tool: Partial<ToolConfig>,
  ): Promise<APIResponse<ToolConfig>> {
    return this.request(`/tools/${id}`, {
      method: "PUT",
      body: JSON.stringify(tool),
    });
  }

  async deleteTool(id: string): Promise<APIResponse<void>> {
    return this.request(`/tools/${id}`, {
      method: "DELETE",
    });
  }

  async testTool(
    id: string,
    input: any,
    context?: any,
  ): Promise<APIResponse<any>> {
    return this.request(`/tools/${id}/test`, {
      method: "POST",
      body: JSON.stringify({ input, context }),
    });
  }

  async getToolAnalytics(id: string): Promise<APIResponse<any>> {
    return this.request(`/tools/${id}/analytics`);
  }

  async getToolExecutionHistory(
    id: string,
    limit?: number,
  ): Promise<APIResponse<any[]>> {
    const params = limit ? `?limit=${limit}` : "";
    return this.request(`/tools/${id}/executions${params}`);
  }

  async validateToolSchema(
    schema: string,
  ): Promise<APIResponse<{ valid: boolean; errors?: string[] }>> {
    return this.request("/tools/validate-schema", {
      method: "POST",
      body: JSON.stringify({ schema }),
    });
  }

  async previewToolExecution(
    toolConfig: any,
    input: any,
  ): Promise<APIResponse<any>> {
    return this.request("/tools/preview", {
      method: "POST",
      body: JSON.stringify({ toolConfig, input }),
    });
  }

  // HITL Management
  async getHITLRequests(): Promise<APIResponse<HITLRequest[]>> {
    return this.request("/hitl/requests");
  }

  async resolveHITLRequest(
    id: string,
    response: any,
  ): Promise<APIResponse<void>> {
    return this.request(`/hitl/requests/${id}/resolve`, {
      method: "POST",
      body: JSON.stringify({ response }),
    });
  }

  async getHITLHistory(
    agentId?: string,
    toolId?: string,
  ): Promise<APIResponse<HITLRequest[]>> {
    const params = new URLSearchParams();
    if (agentId) params.append("agentId", agentId);
    if (toolId) params.append("toolId", toolId);

    return this.request(`/hitl/history?${params.toString()}`);
  }

  // Tool-Agent Hybrid Management
  async getHybrids(): Promise<APIResponse<ToolAgentHybridConfig[]>> {
    return this.request("/hybrids");
  }

  async createHybrid(
    hybrid: ToolAgentHybridConfig,
  ): Promise<APIResponse<ToolAgentHybridConfig>> {
    return this.request("/hybrids", {
      method: "POST",
      body: JSON.stringify(hybrid),
    });
  }

  async updateHybrid(
    id: string,
    hybrid: Partial<ToolAgentHybridConfig>,
  ): Promise<APIResponse<ToolAgentHybridConfig>> {
    return this.request(`/hybrids/${id}`, {
      method: "PUT",
      body: JSON.stringify(hybrid),
    });
  }

  async deleteHybrid(id: string): Promise<APIResponse<void>> {
    return this.request(`/hybrids/${id}`, {
      method: "DELETE",
    });
  }

  async getHybrid(id: string): Promise<APIResponse<ToolAgentHybridConfig>> {
    return this.request(`/hybrids/${id}`);
  }

  async testHybrid(
    id: string,
    input: any,
    context?: any,
  ): Promise<APIResponse<HybridExecutionContext>> {
    return this.request(`/hybrids/${id}/test`, {
      method: "POST",
      body: JSON.stringify({ input, context }),
    });
  }

  async executeHybrid(
    id: string,
    input: any,
    sessionId: string,
    context?: any,
  ): Promise<APIResponse<HybridExecutionContext>> {
    return this.request(`/hybrids/${id}/execute`, {
      method: "POST",
      body: JSON.stringify({ input, sessionId, context }),
    });
  }

  async deployHybrid(
    id: string,
    environment?: "development" | "staging" | "production",
  ): Promise<APIResponse<{ deploymentId: string; endpoint: string }>> {
    return this.request(`/hybrids/${id}/deploy`, {
      method: "POST",
      body: JSON.stringify({ environment }),
    });
  }

  async getHybridAnalytics(
    id: string,
    timeRange?: { start: Date; end: Date },
  ): Promise<APIResponse<HybridAnalytics>> {
    const params = new URLSearchParams();
    if (timeRange) {
      params.append("start", timeRange.start.toISOString());
      params.append("end", timeRange.end.toISOString());
    }
    return this.request(`/hybrids/${id}/analytics?${params.toString()}`);
  }

  async getHybridExecutionHistory(
    id: string,
    limit?: number,
    offset?: number,
  ): Promise<APIResponse<HybridExecutionContext[]>> {
    const params = new URLSearchParams();
    if (limit) params.append("limit", limit.toString());
    if (offset) params.append("offset", offset.toString());
    return this.request(`/hybrids/${id}/executions?${params.toString()}`);
  }

  async cancelHybridExecution(
    hybridId: string,
    executionId: string,
  ): Promise<APIResponse<void>> {
    return this.request(
      `/hybrids/${hybridId}/executions/${executionId}/cancel`,
      {
        method: "POST",
      },
    );
  }

  async validateHybridConfig(
    config: ToolAgentHybridConfig,
  ): Promise<APIResponse<{ valid: boolean; errors?: string[] }>> {
    return this.request("/hybrids/validate", {
      method: "POST",
      body: JSON.stringify({ config }),
    });
  }

  async cloneHybrid(
    id: string,
    newName: string,
  ): Promise<APIResponse<ToolAgentHybridConfig>> {
    return this.request(`/hybrids/${id}/clone`, {
      method: "POST",
      body: JSON.stringify({ newName }),
    });
  }

  async exportHybrid(
    id: string,
    format: "json" | "yaml" | "terraform",
  ): Promise<APIResponse<string>> {
    return this.request(`/hybrids/${id}/export?format=${format}`);
  }

  async importHybrid(
    data: string,
    format: "json" | "yaml",
  ): Promise<APIResponse<ToolAgentHybridConfig>> {
    return this.request("/hybrids/import", {
      method: "POST",
      body: JSON.stringify({ data, format }),
    });
  }
}

export const apiService = new APIService();
