import { SessionState } from "@/types/api";
import { wsService } from "./websocket";

class StateManager {
  private states: Map<string, SessionState> = new Map();
  private subscribers: Map<string, ((state: SessionState) => void)[]> =
    new Map();

  constructor() {
    // Listen for state updates from WebSocket
    wsService.on("state:update", (data) => {
      this.handleStateUpdate(data);
    });

    wsService.on("state:sync", (data) => {
      this.handleStateSync(data);
    });
  }

  private handleStateUpdate(data: { sessionId: string; state: SessionState }) {
    const { sessionId, state } = data;
    this.states.set(sessionId, state);
    this.notifySubscribers(sessionId, state);
  }

  private handleStateSync(data: { states: Record<string, SessionState> }) {
    Object.entries(data.states).forEach(([sessionId, state]) => {
      this.states.set(sessionId, state);
      this.notifySubscribers(sessionId, state);
    });
  }

  private notifySubscribers(sessionId: string, state: SessionState) {
    const subscribers = this.subscribers.get(sessionId) || [];
    subscribers.forEach((callback) => {
      try {
        callback(state);
      } catch (error) {
        console.error("Error in state subscriber:", error);
      }
    });
  }

  public createSession(agentId?: string, toolId?: string): string {
    const sessionId = this.generateSessionId();
    const state: SessionState = {
      id: sessionId,
      agentId,
      toolId,
      context: {},
      memory: [],
      variables: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };

    this.states.set(sessionId, state);

    // Sync with server
    wsService.send("state:create", { sessionId, state });

    return sessionId;
  }

  public getState(sessionId: string): SessionState | null {
    return this.states.get(sessionId) || null;
  }

  public updateState(sessionId: string, updates: Partial<SessionState>, options?: {
    immediate?: boolean;
    skipNotify?: boolean;
  }) {
    const currentState = this.states.get(sessionId);
    if (!currentState) {
      console.warn(`Session ${sessionId} not found`);
      return false;
    }

    const updatedState: SessionState = {
      ...currentState,
      ...updates,
      updatedAt: new Date(),
    };

    this.states.set(sessionId, updatedState);
    
    if (!options?.skipNotify) {
      this.notifySubscribers(sessionId, updatedState);
    }

    // Queue update for batch sync or send immediately
    if (options?.immediate || !this.persistenceEnabled) {
      wsService.send("state:update", { 
        sessionId, 
        updates: this.compressState(updatedState),
        timestamp: Date.now()
      });
    } else {
      this.pendingUpdates.set(sessionId, updates);
    }

    return true;
  }

  public batchUpdateStates(updates: Array<{
    sessionId: string;
    updates: Partial<SessionState>;
  }>) {
    const processedUpdates: any[] = [];
    
    updates.forEach(({ sessionId, updates: stateUpdates }) => {
      const success = this.updateState(sessionId, stateUpdates, { skipNotify: true });
      if (success) {
        processedUpdates.push({
          sessionId,
          updates: this.compressState(stateUpdates as SessionState),
          timestamp: Date.now()
        });
      }
    });

    // Notify all subscribers after batch update
    updates.forEach(({ sessionId }) => {
      const state = this.states.get(sessionId);
      if (state) {
        this.notifySubscribers(sessionId, state);
      }
    });

    // Send batch update to server
    if (processedUpdates.length > 0) {
      wsService.send("state:batch_update", { 
        updates: processedUpdates,
        timestamp: Date.now()
      });
    }
  }

  public updateContext(sessionId: string, context: Record<string, any>) {
    this.updateState(sessionId, { context });
  }

  public updateMemory(sessionId: string, memory: any[]) {
    this.updateState(sessionId, { memory });
  }

  public updateVariables(sessionId: string, variables: Record<string, any>) {
    this.updateState(sessionId, { variables });
  }

  public addToMemory(sessionId: string, item: any) {
    const state = this.getState(sessionId);
    if (state) {
      const updatedMemory = [...state.memory, item];
      this.updateMemory(sessionId, updatedMemory);
    }
  }

  public setVariable(sessionId: string, key: string, value: any) {
    const state = this.getState(sessionId);
    if (state) {
      const updatedVariables = { ...state.variables, [key]: value };
      this.updateVariables(sessionId, updatedVariables);
    }
  }

  public subscribe(sessionId: string, callback: (state: SessionState) => void) {
    if (!this.subscribers.has(sessionId)) {
      this.subscribers.set(sessionId, []);
    }
    this.subscribers.get(sessionId)!.push(callback);

    // Return unsubscribe function
    return () => {
      const subscribers = this.subscribers.get(sessionId);
      if (subscribers) {
        const index = subscribers.indexOf(callback);
        if (index > -1) {
          subscribers.splice(index, 1);
        }
      }
    };
  }

  public deleteSession(sessionId: string) {
    this.states.delete(sessionId);
    this.subscribers.delete(sessionId);

    // Sync with server
    wsService.send("state:delete", { sessionId });
  }

  public getAllSessions(): SessionState[] {
    return Array.from(this.states.values());
  }

  public cleanupExpiredSessions() {
    const now = new Date();
    const expiredSessions: string[] = [];

    this.states.forEach((state, sessionId) => {
      if (state.expiresAt < now) {
        expiredSessions.push(sessionId);
      }
    });

    expiredSessions.forEach((sessionId) => {
      this.deleteSession(sessionId);
    });

    return expiredSessions.length;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const stateManager = new StateManager();

// Cleanup expired sessions every 5 minutes
setInterval(
  () => {
    const cleaned = stateManager.cleanupExpiredSessions();
    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired sessions`);
    }
  },
  5 * 60 * 1000,
);
