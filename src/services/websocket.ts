import { APXMessage } from "@/types/api";

type EventHandler = (data: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private eventHandlers: Map<string, EventHandler[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.connect();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private connect() {
    const wsUrl = import.meta.env.VITE_WS_URL || "ws://localhost:3001";

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log("WebSocket connected");
        this.reconnectAttempts = 0;
        this.send("session:init", { sessionId: this.sessionId });
      };

      this.ws.onmessage = (event) => {
        try {
          const message: APXMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error("Failed to parse WebSocket message:", error);
        }
      };

      this.ws.onclose = () => {
        console.log("WebSocket disconnected");
        this.handleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    } catch (error) {
      console.error("Failed to connect to WebSocket:", error);
      this.handleReconnect();
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay =
        this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      setTimeout(() => {
        console.log(
          `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`,
        );
        this.connect();
      }, delay);
    } else {
      console.error("Max reconnection attempts reached");
    }
  }

  private handleMessage(message: APXMessage) {
    const handlers = this.eventHandlers.get(message.event) || [];
    handlers.forEach((handler) => {
      try {
        handler(message.data);
      } catch (error) {
        console.error("Error in event handler:", error);
      }
    });
  }

  public send(event: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: APXMessage = {
        id: this.generateMessageId(),
        type: "request",
        event,
        data,
        timestamp: Date.now(),
        sessionId: this.sessionId,
      };

      this.ws.send(JSON.stringify(message));
    } else {
      console.warn("WebSocket is not connected");
    }
  }

  public on(event: string, handler: EventHandler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  public off(event: string, handler: EventHandler) {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  public disconnect(reason?: string) {
    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, reason || "Client disconnect");
      this.ws = null;
    }

    this.connectionState = "disconnected";
    this.messageQueue = [];
    this.emit("connection:closed", { reason });
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public isConnected(): boolean {
    return (
      this.ws?.readyState === WebSocket.OPEN &&
      this.connectionState === "connected"
    );
  }

  public getConnectionState(): string {
    return this.connectionState;
  }

  public getLatency(): number {
    return this.latency;
  }

  public getQueueSize(): number {
    return this.messageQueue.length;
  }

  public clearQueue(): void {
    this.messageQueue = [];
    this.emit("queue:cleared", {});
  }

  // Enhanced event subscription with options
  public subscribe(
    event: string,
    handler: EventHandler,
    options?: {
      once?: boolean;
      priority?: number;
    },
  ): () => void {
    if (options?.once) {
      const onceHandler = (data: any) => {
        handler(data);
        this.off(event, onceHandler);
      };
      this.on(event, onceHandler);
      return () => this.off(event, onceHandler);
    } else {
      this.on(event, handler);
      return () => this.off(event, handler);
    }
  }

  // Request-response pattern with timeout
  public async request(
    event: string,
    data: any,
    timeout: number = 30000,
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateMessageId();
      const responseEvent = `${event}:response:${requestId}`;

      const timeoutId = setTimeout(() => {
        this.off(responseEvent, responseHandler);
        reject(new Error(`Request timeout after ${timeout}ms`));
      }, timeout);

      const responseHandler = (responseData: any) => {
        clearTimeout(timeoutId);
        this.off(responseEvent, responseHandler);

        if (responseData.error) {
          reject(new Error(responseData.error));
        } else {
          resolve(responseData);
        }
      };

      this.on(responseEvent, responseHandler);
      this.send(event, { ...data, requestId });
    });
  }
}

export const wsService = new WebSocketService();
