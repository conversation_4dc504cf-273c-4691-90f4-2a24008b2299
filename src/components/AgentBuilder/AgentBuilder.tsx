import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import {
  AlertCircle,
  Check,
  Code,
  Database,
  Play,
  Save,
  Settings,
  Sparkles,
  Loader2,
  CheckCircle2,
  XCircle,
  Bell,
  Clock,
  Zap,
  Activity,
  Users,
  Brain,
  Shield,
  RefreshCw,
  Wifi,
  WifiOff,
  Timer,
  Target,
} from "lucide-react";
import {
  AgentConfig,
  AgentConfigSchema,
  AIProvider,
  HITLRequest,
} from "@/types/api";
import { apiService } from "@/services/api";
import { stateManager } from "@/services/state";
import {
  useRealTimeData,
  useSessionState,
  useWebSocketConnection,
} from "@/hooks/useRealTimeData";

interface AgentBuilderProps {
  agentId?: string;
  onSave?: (agentConfig: AgentConfig) => void;
  onTest?: (agentConfig: AgentConfig) => void;
  onDeploy?: (agentConfig: AgentConfig) => void;
  initialConfig?: AgentConfig;
}

const AgentBuilder: React.FC<AgentBuilderProps> = ({
  agentId,
  onSave = () => {},
  onTest = () => {},
  onDeploy = () => {},
  initialConfig,
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("properties");
  const [agentConfig, setAgentConfig] = useState<AgentConfig>({
    name: "",
    description: "",
    model: "gpt-4o",
    temperature: 0.7,
    maxTokens: 4000,
    stateManagement: {
      enabled: true,
      persistenceType: "redis",
      ttl: 3600,
    },
    memory: {
      enabled: true,
      type: "conversational",
      contextWindow: 10,
    },
    reasoning: {
      enabled: true,
      type: "chain-of-thought",
      steps: 3,
    },
    hitl: {
      enabled: false,
      mode: "optional",
      timeout: 300,
    },
    providers: [],
  });
  const [previewJson, setPreviewJson] = useState<string>("");
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [deploying, setDeploying] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [sessionId] = useState(() => stateManager.createSession(agentId));
  const { state: sessionState } = useSessionState(sessionId);
  const { connected: wsConnected } = useWebSocketConnection();
  const [hitlRequests, setHitlRequests] = useState<HITLRequest[]>([]);
  const [providerMetrics, setProviderMetrics] = useState<
    Record<string, { latency: number; successRate: number; lastUsed: Date }>
  >({});
  const [deploymentStatus, setDeploymentStatus] = useState<
    "idle" | "deploying" | "deployed" | "failed"
  >("idle");
  const [realTimeUpdates, setRealTimeUpdates] = useState<boolean>(true);
  const [autoSave, setAutoSave] = useState<boolean>(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Load initial data and setup real-time subscriptions
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load providers with metrics
        const providersResponse = await apiService.getProviders();
        if (providersResponse.success && providersResponse.data) {
          const enabledProviders = providersResponse.data.filter(
            (p) => p.enabled,
          );
          setProviders(enabledProviders);

          // Initialize provider metrics
          const metrics: Record<
            string,
            { latency: number; successRate: number; lastUsed: Date }
          > = {};
          enabledProviders.forEach((provider) => {
            metrics[provider.id] = {
              latency: provider.latency || 0,
              successRate: provider.successRate || 0,
              lastUsed: new Date(),
            };
          });
          setProviderMetrics(metrics);
        }

        // Load existing agent if agentId provided
        if (agentId) {
          if (initialConfig) {
            setAgentConfig(initialConfig);
          }
        }

        // Load HITL requests for this agent
        if (agentId) {
          const hitlResponse = await apiService.getHITLHistory(agentId);
          if (hitlResponse.success && hitlResponse.data) {
            setHitlRequests(
              hitlResponse.data.filter((req) => req.status === "pending"),
            );
          }
        }
      } catch (error) {
        console.error("Failed to load data:", error);
        toast({
          title: "Error",
          description: "Failed to load agent data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [agentId, initialConfig, toast]);

  // Update preview JSON whenever config changes
  useEffect(() => {
    setPreviewJson(JSON.stringify(agentConfig, null, 2));

    // Validate configuration
    try {
      AgentConfigSchema.parse(agentConfig);
      setValidationErrors([]);
    } catch (error: any) {
      const errors = error.errors?.map(
        (e: any) => `${e.path.join(".")}: ${e.message}`,
      ) || ["Invalid configuration"];
      setValidationErrors(errors);
    }
  }, [agentConfig]);

  // Update session state when config changes
  useEffect(() => {
    if (sessionState) {
      stateManager.updateContext(sessionId, { agentConfig });
    }
  }, [agentConfig, sessionId, sessionState]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && agentConfig.name && agentId) {
      const autoSaveTimer = setTimeout(() => {
        handleSave(true); // Silent save
      }, 5000); // Auto-save every 5 seconds

      return () => clearTimeout(autoSaveTimer);
    }
  }, [agentConfig, autoSave, agentId]);

  // Real-time provider metrics updates
  useEffect(() => {
    if (realTimeUpdates && wsConnected) {
      const interval = setInterval(async () => {
        try {
          const response = await apiService.getProviders();
          if (response.success && response.data) {
            const metrics: Record<
              string,
              { latency: number; successRate: number; lastUsed: Date }
            > = {};
            response.data.forEach((provider) => {
              metrics[provider.id] = {
                latency: provider.latency || 0,
                successRate: provider.successRate || 0,
                lastUsed: new Date(),
              };
            });
            setProviderMetrics(metrics);
          }
        } catch (error) {
          console.error("Failed to update provider metrics:", error);
        }
      }, 10000); // Update every 10 seconds

      return () => clearInterval(interval);
    }
  }, [realTimeUpdates, wsConnected]);

  const updateConfig = (path: string, value: any) => {
    const newConfig = { ...agentConfig };
    const pathParts = path.split(".");
    let current: any = newConfig;

    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]];
    }

    current[pathParts[pathParts.length - 1]] = value;
    setAgentConfig(newConfig);
  };

  const handleSave = async (silent: boolean = false) => {
    try {
      setLoading(true);

      // Validate configuration
      const validatedConfig = AgentConfigSchema.parse(agentConfig);

      let response;
      if (agentId) {
        response = await apiService.updateAgent(agentId, validatedConfig);
      } else {
        response = await apiService.createAgent(validatedConfig);
      }

      if (response.success) {
        setLastSaved(new Date());

        if (!silent) {
          toast({
            title: "Success",
            description: agentId
              ? "Agent updated successfully"
              : "Agent created successfully",
          });
        }

        onSave(response.data!);

        // Update session state with saved config
        stateManager.updateContext(sessionId, {
          agentConfig: response.data,
          lastSaved: new Date(),
        });
      } else {
        throw new Error(response.error || "Failed to save agent");
      }
    } catch (error: any) {
      console.error("Failed to save agent:", error);

      if (!silent) {
        toast({
          title: "Error",
          description: error.message || "Failed to save agent",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      // Comprehensive configuration validation
      const validatedConfig = AgentConfigSchema.parse(agentConfig);

      // Validate provider availability
      if (validatedConfig.providers.length === 0) {
        throw new Error("At least one provider must be configured for testing");
      }

      // Check provider connectivity
      const providerChecks = await Promise.allSettled(
        validatedConfig.providers.map(async (providerId) => {
          const provider = providers.find((p) => p.id === providerId);
          if (!provider) {
            throw new Error(`Provider ${providerId} not found`);
          }
          if (!provider.connected) {
            throw new Error(`Provider ${provider.name} is not connected`);
          }
          return provider;
        }),
      );

      const failedProviders = providerChecks
        .filter((result) => result.status === "rejected")
        .map((result, index) => ({
          providerId: validatedConfig.providers[index],
          error: (result as PromiseRejectedResult).reason.message,
        }));

      if (failedProviders.length === validatedConfig.providers.length) {
        throw new Error(
          `All providers failed: ${failedProviders.map((p) => p.error).join(", ")}`,
        );
      }

      const testInput = {
        message:
          "Hello, this is a comprehensive test message. Please demonstrate your reasoning capabilities, memory usage, and provider fallback mechanisms. Analyze this input and provide a structured response.",
        context: {
          ...(sessionState?.context || {}),
          testMode: true,
          expectedCapabilities: [
            "reasoning",
            "memory_access",
            "provider_selection",
            "structured_response",
          ],
        },
        metadata: {
          testId: `test_${Date.now()}`,
          timestamp: new Date().toISOString(),
          sessionId: sessionId,
          agentConfig: {
            model: validatedConfig.model,
            temperature: validatedConfig.temperature,
            maxTokens: validatedConfig.maxTokens,
            reasoning: validatedConfig.reasoning,
            memory: validatedConfig.memory,
            hitl: validatedConfig.hitl,
          },
        },
      };

      const startTime = Date.now();

      // Use the agent engine directly for comprehensive testing
      let response;
      try {
        const executionContext = await agentEngine.executeAgent(
          validatedConfig,
          testInput,
          sessionId,
          context,
        );
        
        response = {
          success: executionContext.status === "completed",
          data: {
            output: executionContext.output,
            reasoning: executionContext.reasoning,
            performance: executionContext.performance,
            providersUsed: executionContext.reasoning.providersUsed,
            reasoningSteps: executionContext.reasoning.steps,
            hitlTriggered: false, // Will be enhanced with HITL integration
            memoryAccessed: executionContext.memory.accessed.length > 0,
            stateUpdated: true,
          },
          error: executionContext.error,
        };
      } catch (engineError: any) {
        // Fallback to API service if agent engine fails
        console.warn(
          "Agent engine failed, falling back to API service:",
          engineError,
        );
        
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount <= maxRetries) {
          try {
            response = await apiService.testAgent(agentId || "temp", testInput, context);
            break;
          } catch (error: any) {
            retryCount++;
            if (retryCount > maxRetries) {
              throw error;
            }

            // Wait before retry with exponential backoff
            await new Promise((resolve) =>
              setTimeout(resolve, Math.pow(2, retryCount) * 1000),
            );

            toast({
              title: "Retrying Test",
              description: `Attempt ${retryCount + 1}/${maxRetries + 1}`,
            });
          }
        }
      }

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      if (response?.success) {
        const testResultData = {
          ...response.data,
          responseTime,
          timestamp: new Date().toISOString(),
          providersUsed: response.data?.providersUsed || [],
          reasoningSteps: response.data?.reasoningSteps || [],
          hitlTriggered: response.data?.hitlTriggered || false,
          memoryAccessed: response.data?.memoryAccessed || false,
          stateUpdated: response.data?.stateUpdated || false,
          retryCount,
          failedProviders,
          testMetrics: {
            configurationValid: true,
            providersAvailable:
              validatedConfig.providers.length - failedProviders.length,
            totalProviders: validatedConfig.providers.length,
            reasoningEnabled: validatedConfig.reasoning.enabled,
            memoryEnabled: validatedConfig.memory.enabled,
            hitlEnabled: validatedConfig.hitl.enabled,
          },
        };

        setTestResult(testResultData);

        toast({
          title: "Test Successful",
          description: `Agent responded successfully in ${responseTime}ms (${retryCount} retries)`,
        });

        // Update session memory with comprehensive test result
        stateManager.addToMemory(sessionId, {
          type: "agent_test",
          agentId: agentId,
          agentName: agentConfig.name,
          input: testInput,
          output: testResultData,
          timestamp: new Date(),
          metadata: {
            responseTime,
            retryCount,
            providersUsed: testResultData.providersUsed,
            reasoningEnabled: agentConfig.reasoning.enabled,
            reasoningSteps: testResultData.reasoningSteps?.length || 0,
            hitlEnabled: agentConfig.hitl.enabled,
            hitlTriggered: testResultData.hitlTriggered,
            memoryEnabled: agentConfig.memory.enabled,
            memoryAccessed: testResultData.memoryAccessed,
            stateManagementEnabled: agentConfig.stateManagement.enabled,
            stateUpdated: testResultData.stateUpdated,
            failedProviders: failedProviders.length,
            successfulProviders:
              validatedConfig.providers.length - failedProviders.length,
          },
        });

        // Update provider metrics based on test results
        if (testResultData.providersUsed?.length > 0) {
          const updatedMetrics = { ...providerMetrics };
          testResultData.providersUsed.forEach((providerId: string) => {
            if (updatedMetrics[providerId]) {
              updatedMetrics[providerId].lastUsed = new Date();
              updatedMetrics[providerId].latency =
                responseTime / testResultData.providersUsed.length;
              updatedMetrics[providerId].successRate = Math.min(
                1.0,
                updatedMetrics[providerId].successRate + 0.1,
              );
            }
          });
          setProviderMetrics(updatedMetrics);
        }

        // Update failed provider metrics
        failedProviders.forEach(({ providerId }) => {
          if (providerMetrics[providerId]) {
            const updatedMetrics = { ...providerMetrics };
            updatedMetrics[providerId].successRate = Math.max(
              0.0,
              updatedMetrics[providerId].successRate - 0.1,
            );
            setProviderMetrics(updatedMetrics);
          }
        });
      } else {
        throw new Error(
          response?.error || "Test failed - no response received",
        );
      }
    } catch (error: any) {
      console.error("Agent test failed:", error);
      const failedResult = {
        error: error.message,
        timestamp: new Date().toISOString(),
        responseTime: 0,
        testMetrics: {
          configurationValid: false,
          errorType: error.name || "UnknownError",
          errorCategory: error.code || "UNKNOWN",
        },
      };

      setTestResult(failedResult);

      toast({
        title: "Test Failed",
        description: error.message || "Agent test failed",
        variant: "destructive",
      });

      // Log failed test to session memory
      stateManager.addToMemory(sessionId, {
        type: "agent_test_failed",
        agentId: agentId,
        agentName: agentConfig.name,
        error: error.message,
        timestamp: new Date(),
        metadata: {
          configurationAttempted: agentConfig,
          errorType: error.name,
          errorCode: error.code,
        },
      });
    } finally {
      setTesting(false);
    }
  };

  const handleDeploy = async () => {
    try {
      setDeploying(true);
      setDeploymentStatus("deploying");

      // Validate configuration first
      const validatedConfig = AgentConfigSchema.parse(agentConfig);

      if (!agentId) {
        throw new Error("Agent must be saved before deployment");
      }

      // Pre-deployment checks
      const preDeploymentChecks = [
        { name: "Configuration Validation", status: "completed" },
        { name: "Provider Connectivity", status: "running" },
        { name: "Memory Setup", status: "pending" },
        { name: "State Management", status: "pending" },
      ];

      // Simulate deployment progress
      for (let i = 0; i < preDeploymentChecks.length; i++) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        preDeploymentChecks[i].status = "completed";
      }

      const response = await apiService.deployAgent(agentId);

      if (response.success) {
        setDeploymentStatus("deployed");

        toast({
          title: "Deployment Successful",
          description: `Agent deployed at: ${response.data?.endpoint}`,
        });

        // Update session state with deployment info
        stateManager.updateContext(sessionId, {
          deploymentInfo: {
            deploymentId: response.data?.deploymentId,
            endpoint: response.data?.endpoint,
            deployedAt: new Date(),
            status: "active",
          },
        });

        onDeploy(agentConfig);
      } else {
        setDeploymentStatus("failed");
        throw new Error(response.error || "Deployment failed");
      }
    } catch (error: any) {
      console.error("Deployment failed:", error);
      setDeploymentStatus("failed");

      toast({
        title: "Deployment Failed",
        description: error.message || "Failed to deploy agent",
        variant: "destructive",
      });
    } finally {
      setDeploying(false);
    }
  };

  // HITL request handler
  const handleHITLRequest = useCallback(
    async (requestId: string, response: any) => {
      try {
        const result = await apiService.resolveHITLRequest(requestId, response);
        if (result.success) {
          setHitlRequests((prev) => prev.filter((req) => req.id !== requestId));
          toast({
            title: "HITL Request Resolved",
            description: "Human input has been processed",
          });

          // Update session memory
          stateManager.addToMemory(sessionId, {
            type: "hitl_resolution",
            requestId,
            response,
            timestamp: new Date(),
          });
        }
      } catch (error: any) {
        toast({
          title: "Error",
          description: "Failed to resolve HITL request",
          variant: "destructive",
        });
      }
    },
    [sessionId, toast],
  );

  // Smart provider selection based on metrics
  const getOptimalProviders = useMemo(() => {
    return providers
      .filter((p) => agentConfig.providers.includes(p.id))
      .sort((a, b) => {
        const aMetrics = providerMetrics[a.id];
        const bMetrics = providerMetrics[b.id];

        if (!aMetrics || !bMetrics) return 0;

        // Score based on success rate (70%) and latency (30%)
        const aScore =
          aMetrics.successRate * 0.7 + ((1000 - aMetrics.latency) / 1000) * 0.3;
        const bScore =
          bMetrics.successRate * 0.7 + ((1000 - bMetrics.latency) / 1000) * 0.3;

        return bScore - aScore;
      });
  }, [providers, agentConfig.providers, providerMetrics]);

  return (
    <div className="flex flex-col h-full w-full bg-background p-6 gap-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Agent Builder</h1>
          <p className="text-muted-foreground">
            Configure your AI agent with state management, memory, and reasoning
            capabilities
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* Connection Status */}
          <div className="flex items-center gap-2 mr-4">
            {wsConnected ? (
              <div className="flex items-center gap-1 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-xs">Connected</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-xs">Disconnected</span>
              </div>
            )}

            {lastSaved && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span className="text-xs">
                  Saved {lastSaved.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>

          {/* HITL Notifications */}
          {hitlRequests.length > 0 && (
            <Button variant="outline" size="sm" className="relative">
              <Bell className="h-4 w-4 mr-1" />
              HITL ({hitlRequests.length})
              <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-pulse" />
            </Button>
          )}

          <Button
            variant="outline"
            onClick={handleTest}
            disabled={testing || validationErrors.length > 0}
          >
            {testing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Play className="mr-2 h-4 w-4" />
            )}
            {testing ? "Testing..." : "Test"}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleSave(false)}
            disabled={loading || validationErrors.length > 0}
          >
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            {loading ? "Saving..." : "Save"}
          </Button>
          <Button
            onClick={handleDeploy}
            disabled={deploying || !agentId || validationErrors.length > 0}
            className={
              deploymentStatus === "deployed"
                ? "bg-green-600 hover:bg-green-700"
                : ""
            }
          >
            {deploying ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : deploymentStatus === "deployed" ? (
              <CheckCircle2 className="mr-2 h-4 w-4" />
            ) : (
              <Check className="mr-2 h-4 w-4" />
            )}
            {deploying
              ? "Deploying..."
              : deploymentStatus === "deployed"
                ? "Deployed"
                : "Deploy"}
          </Button>
        </div>
      </div>

      <div className="flex gap-6 h-full">
        {/* Configuration Panel */}
        <div className="w-2/3">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-6 mb-6">
              <TabsTrigger value="properties">Properties</TabsTrigger>
              <TabsTrigger value="providers">Providers</TabsTrigger>
              <TabsTrigger value="state">State Management</TabsTrigger>
              <TabsTrigger value="memory">Memory</TabsTrigger>
              <TabsTrigger value="reasoning">Reasoning</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TabsList>

            <TabsContent value="properties" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Agent Properties</CardTitle>
                  <CardDescription>
                    Configure the basic properties of your agent
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={agentConfig.name}
                        onChange={(e) => updateConfig("name", e.target.value)}
                        placeholder="My AI Agent"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="model">Model</Label>
                      <Select
                        value={agentConfig.model}
                        onValueChange={(value) => updateConfig("model", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                          <SelectItem value="gpt-4-turbo">
                            GPT-4 Turbo
                          </SelectItem>
                          <SelectItem value="claude-3-5-sonnet-20241022">
                            Claude 3.5 Sonnet
                          </SelectItem>
                          <SelectItem value="claude-3-opus-20240229">
                            Claude 3 Opus
                          </SelectItem>
                          <SelectItem value="gemini-1.5-pro">
                            Gemini 1.5 Pro
                          </SelectItem>
                          <SelectItem value="mistral-large-latest">
                            Mistral Large
                          </SelectItem>
                          <SelectItem value="llama-3.1-70b-versatile">
                            Llama 3.1 70B (Groq)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={agentConfig.description}
                      onChange={(e) =>
                        updateConfig("description", e.target.value)
                      }
                      placeholder="Describe what this agent does"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="temperature">
                        Temperature ({agentConfig.temperature})
                      </Label>
                      <Input
                        id="temperature"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={agentConfig.temperature}
                        onChange={(e) =>
                          updateConfig(
                            "temperature",
                            parseFloat(e.target.value),
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxTokens">Max Tokens</Label>
                      <Input
                        id="maxTokens"
                        type="number"
                        value={agentConfig.maxTokens}
                        onChange={(e) =>
                          updateConfig("maxTokens", parseInt(e.target.value))
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="providers" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Provider Selection</CardTitle>
                  <CardDescription>
                    Choose which AI providers this agent can use, with fallback
                    priority
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {providers.map((provider) => {
                      const isSelected = agentConfig.providers.includes(
                        provider.id,
                      );
                      const metrics = providerMetrics[provider.id];
                      const isOptimal =
                        getOptimalProviders[0]?.id === provider.id;

                      return (
                        <div
                          key={provider.id}
                          className={`flex items-center justify-between p-3 border rounded-lg transition-all ${
                            isOptimal ? "border-green-300 bg-green-50" : ""
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <Switch
                              checked={isSelected}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  updateConfig("providers", [
                                    ...agentConfig.providers,
                                    provider.id,
                                  ]);
                                } else {
                                  updateConfig(
                                    "providers",
                                    agentConfig.providers.filter(
                                      (id) => id !== provider.id,
                                    ),
                                  );
                                }
                              }}
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <div className="font-medium">
                                  {provider.name}
                                </div>
                                {isOptimal && (
                                  <Badge
                                    variant="outline"
                                    className="bg-green-100 text-green-800 border-green-200"
                                  >
                                    <Target className="h-3 w-3 mr-1" />
                                    Optimal
                                  </Badge>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {provider.models.slice(0, 2).join(", ")}
                                {provider.models.length > 2 &&
                                  ` +${provider.models.length - 2} more`}
                              </div>
                              {metrics && (
                                <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                                  <span className="flex items-center gap-1">
                                    <Timer className="h-3 w-3" />
                                    {metrics.latency}ms
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Activity className="h-3 w-3" />
                                    {(metrics.successRate * 100).toFixed(1)}%
                                  </span>
                                  <span>
                                    Last used:{" "}
                                    {metrics.lastUsed.toLocaleTimeString()}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {provider.connected ? (
                              <Badge
                                variant="outline"
                                className="bg-green-50 text-green-700 border-green-200"
                              >
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Connected
                              </Badge>
                            ) : (
                              <Badge
                                variant="outline"
                                className="bg-red-50 text-red-700 border-red-200"
                              >
                                <XCircle className="h-3 w-3 mr-1" />
                                Disconnected
                              </Badge>
                            )}
                            <Badge variant="secondary">
                              Priority {provider.priority}
                            </Badge>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {agentConfig.providers.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                      <p>
                        No providers selected. Agent will use system default.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="state" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>State Management</CardTitle>
                  <CardDescription>
                    Configure how your agent manages and persists state
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="stateEnabled">
                        Enable State Management
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Allow the agent to maintain state between interactions
                      </p>
                    </div>
                    <Switch
                      id="stateEnabled"
                      checked={agentConfig.stateManagement.enabled}
                      onCheckedChange={(checked) =>
                        updateConfig("stateManagement.enabled", checked)
                      }
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="persistenceType">Persistence Type</Label>
                      <Select
                        value={agentConfig.stateManagement.persistenceType}
                        onValueChange={(value) =>
                          updateConfig("stateManagement.persistenceType", value)
                        }
                        disabled={!agentConfig.stateManagement.enabled}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select persistence type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="redis">Redis</SelectItem>
                          <SelectItem value="postgres">PostgreSQL</SelectItem>
                          <SelectItem value="memory">In-Memory</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="ttl">Time-to-Live (seconds)</Label>
                      <Input
                        id="ttl"
                        type="number"
                        value={agentConfig.stateManagement.ttl}
                        onChange={(e) =>
                          updateConfig(
                            "stateManagement.ttl",
                            parseInt(e.target.value),
                          )
                        }
                        disabled={!agentConfig.stateManagement.enabled}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="memory" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Memory Settings</CardTitle>
                  <CardDescription>
                    Configure how your agent remembers past interactions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="memoryEnabled">Enable Memory</Label>
                      <p className="text-sm text-muted-foreground">
                        Allow the agent to remember past conversations
                      </p>
                    </div>
                    <Switch
                      id="memoryEnabled"
                      checked={agentConfig.memory.enabled}
                      onCheckedChange={(checked) =>
                        updateConfig("memory.enabled", checked)
                      }
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="memoryType">Memory Type</Label>
                      <Select
                        value={agentConfig.memory.type}
                        onValueChange={(value) =>
                          updateConfig("memory.type", value)
                        }
                        disabled={!agentConfig.memory.enabled}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select memory type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="conversational">
                            Conversational
                          </SelectItem>
                          <SelectItem value="summarization">
                            Summarization
                          </SelectItem>
                          <SelectItem value="vector">Vector</SelectItem>
                          <SelectItem value="hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="contextWindow">Context Window Size</Label>
                      <Input
                        id="contextWindow"
                        type="number"
                        value={agentConfig.memory.contextWindow}
                        onChange={(e) =>
                          updateConfig(
                            "memory.contextWindow",
                            parseInt(e.target.value),
                          )
                        }
                        disabled={!agentConfig.memory.enabled}
                      />
                      <p className="text-xs text-muted-foreground">
                        Number of past messages to include in context
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reasoning" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Reasoning Capabilities</CardTitle>
                  <CardDescription>
                    Configure how your agent thinks and reasons
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="reasoningEnabled">
                        Enable Advanced Reasoning
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Allow the agent to use advanced reasoning techniques
                      </p>
                    </div>
                    <Switch
                      id="reasoningEnabled"
                      checked={agentConfig.reasoning.enabled}
                      onCheckedChange={(checked) =>
                        updateConfig("reasoning.enabled", checked)
                      }
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="reasoningType">Reasoning Type</Label>
                      <Select
                        value={agentConfig.reasoning.type}
                        onValueChange={(value) =>
                          updateConfig("reasoning.type", value)
                        }
                        disabled={!agentConfig.reasoning.enabled}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select reasoning type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="chain-of-thought">
                            Chain of Thought
                          </SelectItem>
                          <SelectItem value="tree-of-thought">
                            Tree of Thought
                          </SelectItem>
                          <SelectItem value="react">ReAct</SelectItem>
                          <SelectItem value="reflexion">Reflexion</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="steps">Reasoning Steps</Label>
                      <Input
                        id="steps"
                        type="number"
                        value={agentConfig.reasoning.steps}
                        onChange={(e) =>
                          updateConfig(
                            "reasoning.steps",
                            parseInt(e.target.value),
                          )
                        }
                        disabled={!agentConfig.reasoning.enabled}
                      />
                      <p className="text-xs text-muted-foreground">
                        Number of reasoning steps to perform
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Human-in-the-Loop (HITL)</h4>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="hitlEnabled">Enable HITL</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow human intervention during reasoning
                        </p>
                      </div>
                      <Switch
                        id="hitlEnabled"
                        checked={agentConfig.hitl.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig("hitl.enabled", checked)
                        }
                      />
                    </div>

                    {agentConfig.hitl.enabled && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="hitlMode">HITL Mode</Label>
                          <Select
                            value={agentConfig.hitl.mode}
                            onValueChange={(value) =>
                              updateConfig("hitl.mode", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select HITL mode" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="manual">
                                Manual - Always require human input
                              </SelectItem>
                              <SelectItem value="auto">
                                Auto - Human input when needed
                              </SelectItem>
                              <SelectItem value="optional">
                                Optional - Human can intervene
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="hitlTimeout">
                            HITL Timeout (seconds)
                          </Label>
                          <Input
                            id="hitlTimeout"
                            type="number"
                            value={agentConfig.hitl.timeout}
                            onChange={(e) =>
                              updateConfig(
                                "hitl.timeout",
                                parseInt(e.target.value),
                              )
                            }
                          />
                          <p className="text-xs text-muted-foreground">
                            How long to wait for human input before proceeding
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="monitoring" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Real-time Monitoring</CardTitle>
                  <CardDescription>
                    Monitor agent performance, provider metrics, and system
                    health
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label>Real-time Updates</Label>
                        <Switch
                          checked={realTimeUpdates}
                          onCheckedChange={setRealTimeUpdates}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Enable live provider metrics and performance monitoring
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label>Auto-save</Label>
                        <Switch
                          checked={autoSave}
                          onCheckedChange={setAutoSave}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Automatically save changes every 5 seconds
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Provider Performance</h4>
                    <div className="space-y-3">
                      {getOptimalProviders.map((provider, index) => {
                        const metrics = providerMetrics[provider.id];
                        if (!metrics) return null;

                        return (
                          <div
                            key={provider.id}
                            className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                          >
                            <div className="flex items-center gap-3">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  index === 0
                                    ? "bg-green-500"
                                    : index === 1
                                      ? "bg-yellow-500"
                                      : "bg-gray-400"
                                }`}
                              />
                              <div>
                                <div className="font-medium text-sm">
                                  {provider.name}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Rank #{index + 1}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium">
                                {metrics.latency}ms
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {(metrics.successRate * 100).toFixed(1)}%
                                success
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">HITL Queue</h4>
                    {hitlRequests.length > 0 ? (
                      <div className="space-y-2">
                        {hitlRequests.slice(0, 3).map((request) => (
                          <div
                            key={request.id}
                            className="flex items-center justify-between p-3 border rounded-lg"
                          >
                            <div className="flex-1">
                              <div className="font-medium text-sm">
                                {request.type}
                              </div>
                              <div className="text-xs text-muted-foreground truncate">
                                {request.prompt}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  request.priority === "urgent"
                                    ? "destructive"
                                    : "secondary"
                                }
                              >
                                {request.priority}
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  handleHITLRequest(request.id, {
                                    approved: true,
                                  })
                                }
                              >
                                Resolve
                              </Button>
                            </div>
                          </div>
                        ))}
                        {hitlRequests.length > 3 && (
                          <div className="text-center text-sm text-muted-foreground">
                            +{hitlRequests.length - 3} more requests
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No pending HITL requests</p>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Deployment Status</h4>
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          deploymentStatus === "deployed"
                            ? "bg-green-500"
                            : deploymentStatus === "deploying"
                              ? "bg-yellow-500 animate-pulse"
                              : deploymentStatus === "failed"
                                ? "bg-red-500"
                                : "bg-gray-400"
                        }`}
                      />
                      <span className="text-sm font-medium capitalize">
                        {deploymentStatus === "idle"
                          ? "Not Deployed"
                          : deploymentStatus}
                      </span>
                      {deploymentStatus === "deploying" && (
                        <Progress value={75} className="w-24 h-2" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview Panel */}
        <div className="w-1/3">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Code className="mr-2 h-5 w-5" />
                Configuration Preview
              </CardTitle>
              <CardDescription>
                Real-time preview of your agent configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Settings className="h-3 w-3" />
                    {agentConfig.model}
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Sparkles className="h-3 w-3" />
                    Temp: {agentConfig.temperature}
                  </Badge>
                  {agentConfig.stateManagement.enabled && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <Database className="h-3 w-3" />
                      {agentConfig.stateManagement.persistenceType}
                    </Badge>
                  )}
                  {agentConfig.providers.length > 0 && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <CheckCircle2 className="h-3 w-3" />
                      {agentConfig.providers.length} Provider
                      {agentConfig.providers.length !== 1 ? "s" : ""}
                    </Badge>
                  )}
                  {agentConfig.hitl.enabled && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200"
                    >
                      <AlertCircle className="h-3 w-3" />
                      HITL: {agentConfig.hitl.mode}
                    </Badge>
                  )}
                </div>

                {validationErrors.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-destructive">
                      Validation Errors:
                    </h4>
                    <div className="space-y-1">
                      {validationErrors.map((error, index) => (
                        <div
                          key={index}
                          className="text-xs text-destructive bg-destructive/10 p-2 rounded"
                        >
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {testResult && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium flex items-center gap-2">
                      Test Result:
                      {testResult.error ? (
                        <Badge variant="destructive" className="text-xs">
                          <XCircle className="h-3 w-3 mr-1" />
                          Failed
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="text-xs bg-green-50 text-green-700 border-green-200"
                        >
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          Success
                        </Badge>
                      )}
                    </h4>

                    {!testResult.error && testResult.responseTime && (
                      <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                        <span className="flex items-center gap-1">
                          <Timer className="h-3 w-3" />
                          {testResult.responseTime}ms
                        </span>
                        {testResult.providersUsed?.length > 0 && (
                          <span className="flex items-center gap-1">
                            <Zap className="h-3 w-3" />
                            {testResult.providersUsed.length} provider(s)
                          </span>
                        )}
                        {testResult.reasoningSteps?.length > 0 && (
                          <span className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            {testResult.reasoningSteps.length} reasoning steps
                          </span>
                        )}
                        {testResult.hitlTriggered && (
                          <span className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            HITL triggered
                          </span>
                        )}
                      </div>
                    )}

                    <div className="text-xs bg-muted p-3 rounded border max-h-[300px] overflow-auto">
                      <pre>{JSON.stringify(testResult, null, 2)}</pre>
                    </div>
                  </div>
                )}

                <ScrollArea className="h-[400px] rounded-md border p-4 bg-muted/50">
                  <pre className="text-xs">{previewJson}</pre>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AgentBuilder;
