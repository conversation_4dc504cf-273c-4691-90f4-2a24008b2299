import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { Slider } from "@/components/ui/slider";
import {
  AlertCircle,
  Check,
  Code,
  Database,
  Play,
  Save,
  Settings,
  Sparkles,
  Loader2,
  CheckCircle2,
  XCircle,
  Bell,
  Clock,
  Zap,
  Activity,
  Users,
  Brain,
  Shield,
  RefreshCw,
  Wifi,
  WifiOff,
  Timer,
  Target,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  Copy,
  Trash2,
  Edit,
  Eye,
  EyeOff,
  GitBranch,
  Layers,
  Network,
  Cpu,
  HardDrive,
  BarChart3,
  TrendingUp,
  Download,
  Upload,
  FileText,
  Workflow,
  Bot,
  Wrench,
  Link,
  Unlink,
} from "lucide-react";
import {
  ToolAgentHybridConfig,
  ToolAgentHybridConfigSchema,
  AgentConfig,
  ToolConfig,
  HybridExecutionContext,
  HybridAnalytics,
} from "@/types/api";
import { apiService } from "@/services/api";
import { stateManager } from "@/services/state";
import { hybridEngine } from "@/services/hybridEngine";
import {
  useRealTimeData,
  useSessionState,
  useWebSocketConnection,
} from "@/hooks/useRealTimeData";

interface HybridBuilderProps {
  hybridId?: string;
  onSave?: (hybridConfig: ToolAgentHybridConfig) => void;
  onTest?: (hybridConfig: ToolAgentHybridConfig) => void;
  onDeploy?: (hybridConfig: ToolAgentHybridConfig) => void;
  initialConfig?: ToolAgentHybridConfig;
}

const HybridBuilder: React.FC<HybridBuilderProps> = ({
  hybridId,
  onSave = () => {},
  onTest = () => {},
  onDeploy = () => {},
  initialConfig,
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("composition");
  const [hybridConfig, setHybridConfig] = useState<ToolAgentHybridConfig>({
    name: "",
    description: "",
    version: "1.0.0",
    agentConfig: {
      inline: {
        name: "Hybrid Agent",
        description: "Agent for hybrid execution",
        model: "gpt-4o",
        temperature: 0.7,
        maxTokens: 4000,
        stateManagement: {
          enabled: true,
          persistenceType: "redis",
          ttl: 3600,
        },
        memory: {
          enabled: true,
          type: "conversational",
          contextWindow: 10,
        },
        reasoning: {
          enabled: true,
          type: "chain-of-thought",
          steps: 3,
        },
        hitl: {
          enabled: false,
          mode: "optional",
          timeout: 300,
          fallbackStrategy: "none",
          tags: [],
        },
        providers: [],
      },
    },
    toolConfigs: [],
    orchestration: {
      mode: "sequential",
      maxConcurrentTools: 3,
      timeoutMs: 60000,
      retryStrategy: {
        enabled: true,
        maxAttempts: 3,
        backoffMultiplier: 2,
      },
      fallbackStrategy: "best_effort",
    },
    reasoning: {
      toolSelectionStrategy: "agent_driven",
      contextPropagation: {
        enabled: true,
        includeToolResults: true,
        includeAgentMemory: true,
        maxContextSize: 10000,
      },
      adaptiveBehavior: {
        enabled: false,
        learningRate: 0.1,
        performanceThreshold: 0.8,
      },
    },
    stateManagement: {
      enabled: true,
      persistenceType: "redis",
      ttl: 3600,
      sharedContext: true,
      isolatedExecution: false,
    },
    security: {
      accessControl: {
        enabled: true,
        requiredPermissions: [],
        roleBasedAccess: false,
      },
      dataIsolation: {
        enabled: true,
        encryptSensitiveData: true,
        auditTrail: true,
      },
      rateLimiting: {
        enabled: true,
        requestsPerMinute: 60,
        burstLimit: 10,
      },
    },
    monitoring: {
      enabled: true,
      trackPerformance: true,
      trackToolUsage: true,
      trackAgentDecisions: true,
      alerting: {
        enabled: false,
        errorThreshold: 0.1,
        latencyThreshold: 5000,
      },
    },
    deployment: {
      environment: "development",
      scalingPolicy: {
        enabled: false,
        minInstances: 1,
        maxInstances: 5,
        targetCpuUtilization: 70,
      },
      healthCheck: {
        enabled: true,
        intervalMs: 30000,
        timeoutMs: 5000,
      },
    },
    metadata: {
      tags: [],
      category: "automation",
      status: "draft",
    },
  });

  const [previewJson, setPreviewJson] = useState<string>("");
  const [availableAgents, setAvailableAgents] = useState<AgentConfig[]>([]);
  const [availableTools, setAvailableTools] = useState<ToolConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [deploying, setDeploying] = useState(false);
  const [testResult, setTestResult] = useState<HybridExecutionContext | null>(
    null,
  );
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [sessionId] = useState(() =>
    stateManager.createSession(undefined, hybridId),
  );
  const { state: sessionState } = useSessionState(sessionId);
  const { connected: wsConnected } = useWebSocketConnection();
  const [analytics, setAnalytics] = useState<HybridAnalytics | null>(null);
  const [executionHistory, setExecutionHistory] = useState<
    HybridExecutionContext[]
  >([]);
  const [realTimeUpdates, setRealTimeUpdates] = useState<boolean>(true);
  const [autoSave, setAutoSave] = useState<boolean>(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [deploymentStatus, setDeploymentStatus] = useState<
    "idle" | "deploying" | "deployed" | "failed"
  >("idle");
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [testInput, setTestInput] = useState("{}");

  // Load initial data and setup real-time subscriptions
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load available agents
        const agentsResponse = await apiService.getAgents();
        if (agentsResponse.success && agentsResponse.data) {
          setAvailableAgents(agentsResponse.data);
        }

        // Load available tools
        const toolsResponse = await apiService.getTools();
        if (toolsResponse.success && toolsResponse.data) {
          setAvailableTools(toolsResponse.data);
        }

        // Load existing hybrid if hybridId provided
        if (hybridId) {
          if (initialConfig) {
            setHybridConfig(initialConfig);
          } else {
            const hybridResponse = await apiService.getHybrid(hybridId);
            if (hybridResponse.success && hybridResponse.data) {
              setHybridConfig(hybridResponse.data);
            }
          }
        }

        // Load analytics and execution history
        if (hybridId) {
          const analyticsResponse =
            await apiService.getHybridAnalytics(hybridId);
          if (analyticsResponse.success && analyticsResponse.data) {
            setAnalytics(analyticsResponse.data);
          }

          const historyResponse = await apiService.getHybridExecutionHistory(
            hybridId,
            20,
          );
          if (historyResponse.success && historyResponse.data) {
            setExecutionHistory(historyResponse.data);
          }
        }
      } catch (error) {
        console.error("Failed to load data:", error);
        toast({
          title: "Error",
          description: "Failed to load hybrid data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [hybridId, initialConfig, toast]);

  // Update preview JSON whenever config changes
  useEffect(() => {
    setPreviewJson(JSON.stringify(hybridConfig, null, 2));

    // Validate configuration
    try {
      ToolAgentHybridConfigSchema.parse(hybridConfig);
      setValidationErrors([]);
    } catch (error: any) {
      const errors = error.errors?.map(
        (e: any) => `${e.path.join(".")}: ${e.message}`,
      ) || ["Invalid configuration"];
      setValidationErrors(errors);
    }
  }, [hybridConfig]);

  // Update session state when config changes
  useEffect(() => {
    if (sessionState) {
      stateManager.updateContext(sessionId, { hybridConfig });
    }
  }, [hybridConfig, sessionId, sessionState]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && hybridConfig.name && hybridId) {
      const autoSaveTimer = setTimeout(() => {
        handleSave(true); // Silent save
      }, 5000); // Auto-save every 5 seconds

      return () => clearTimeout(autoSaveTimer);
    }
  }, [hybridConfig, autoSave, hybridId]);

  // Real-time updates
  useEffect(() => {
    if (realTimeUpdates && wsConnected && hybridId) {
      const handleHybridEvent = (event: any) => {
        if (event.hybridId === hybridId) {
          // Update analytics or execution history based on event
          if (
            event.type === "hybrid:execution_completed" ||
            event.type === "hybrid:execution_failed"
          ) {
            loadAnalytics();
            loadExecutionHistory();
          }
        }
      };

      hybridEngine.onEvent("hybrid:execution_started", handleHybridEvent);
      hybridEngine.onEvent("hybrid:execution_completed", handleHybridEvent);
      hybridEngine.onEvent("hybrid:execution_failed", handleHybridEvent);

      return () => {
        hybridEngine.offEvent("hybrid:execution_started", handleHybridEvent);
        hybridEngine.offEvent("hybrid:execution_completed", handleHybridEvent);
        hybridEngine.offEvent("hybrid:execution_failed", handleHybridEvent);
      };
    }
  }, [realTimeUpdates, wsConnected, hybridId]);

  const loadAnalytics = async () => {
    if (!hybridId) return;
    try {
      const response = await apiService.getHybridAnalytics(hybridId);
      if (response.success && response.data) {
        setAnalytics(response.data);
      }
    } catch (error) {
      console.error("Failed to load analytics:", error);
    }
  };

  const loadExecutionHistory = async () => {
    if (!hybridId) return;
    try {
      const response = await apiService.getHybridExecutionHistory(hybridId, 20);
      if (response.success && response.data) {
        setExecutionHistory(response.data);
      }
    } catch (error) {
      console.error("Failed to load execution history:", error);
    }
  };

  const updateConfig = (path: string, value: any) => {
    const newConfig = { ...hybridConfig };
    const pathParts = path.split(".");
    let current: any = newConfig;

    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {};
      }
      current = current[pathParts[i]];
    }

    current[pathParts[pathParts.length - 1]] = value;
    setHybridConfig(newConfig);
  };

  const handleSave = async (silent: boolean = false) => {
    try {
      setLoading(true);

      // Validate configuration
      const validatedConfig = ToolAgentHybridConfigSchema.parse({
        ...hybridConfig,
        metadata: {
          ...hybridConfig.metadata,
          updatedAt: new Date(),
        },
      });

      let response;
      if (hybridId) {
        response = await apiService.updateHybrid(hybridId, validatedConfig);
      } else {
        response = await apiService.createHybrid({
          ...validatedConfig,
          metadata: {
            ...validatedConfig.metadata,
            createdAt: new Date(),
          },
        });
      }

      if (response.success) {
        setLastSaved(new Date());

        if (!silent) {
          toast({
            title: "Success",
            description: hybridId
              ? "Hybrid updated successfully"
              : "Hybrid created successfully",
          });
        }

        onSave(response.data!);

        // Update session state with saved config
        stateManager.updateContext(sessionId, {
          hybridConfig: response.data,
          lastSaved: new Date(),
        });
      } else {
        throw new Error(response.error || "Failed to save hybrid");
      }
    } catch (error: any) {
      console.error("Failed to save hybrid:", error);

      if (!silent) {
        toast({
          title: "Error",
          description: error.message || "Failed to save hybrid",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      // Comprehensive configuration validation
      const validatedConfig = ToolAgentHybridConfigSchema.parse(hybridConfig);

      // Validate that we have both agent and tools configured
      if (
        !validatedConfig.agentConfig.inline &&
        !validatedConfig.agentConfig.agentId
      ) {
        throw new Error("Agent configuration is required for hybrid execution");
      }

      if (validatedConfig.toolConfigs.length === 0) {
        throw new Error(
          "At least one tool must be configured for hybrid execution",
        );
      }

      // Validate tool configurations
      const toolValidationErrors: string[] = [];
      validatedConfig.toolConfigs.forEach((toolConfig, index) => {
        if (!toolConfig.inline && !toolConfig.toolId) {
          toolValidationErrors.push(
            `Tool ${index + 1}: Must have either inline configuration or tool ID`,
          );
        }
        if (toolConfig.inline && !toolConfig.inline.endpoint) {
          toolValidationErrors.push(
            `Tool ${index + 1}: Inline tool must have an endpoint`,
          );
        }
      });

      if (toolValidationErrors.length > 0) {
        throw new Error(
          `Tool validation failed: ${toolValidationErrors.join(", ")}`,
        );
      }

      let input;
      try {
        input = JSON.parse(testInput);
      } catch (error) {
        throw new Error(
          "Invalid JSON in test input - please provide valid JSON",
        );
      }

      // Validate input structure
      if (typeof input !== "object" || input === null) {
        throw new Error("Test input must be a valid JSON object");
      }

      const context = {
        ...(sessionState?.context || {}),
        testMode: true,
        hybridConfig: {
          orchestrationMode: validatedConfig.orchestration.mode,
          toolCount: validatedConfig.toolConfigs.length,
          agentModel: validatedConfig.agentConfig.inline?.model || "referenced",
          securityEnabled: validatedConfig.security.accessControl.enabled,
          monitoringEnabled: validatedConfig.monitoring.enabled,
        },
      };

      const startTime = Date.now();

      // Execute hybrid with comprehensive error handling
      let response;
      try {
        // Use the hybrid engine directly for more comprehensive testing
        const executionContext = await hybridEngine.executeHybrid(
          validatedConfig,
          input,
          sessionId,
          context,
        );

        response = {
          success: executionContext.status === "completed",
          data: executionContext,
          error: executionContext.error,
        };
      } catch (engineError: any) {
        // Fallback to API service if hybrid engine fails
        console.warn(
          "Hybrid engine failed, falling back to API service:",
          engineError,
        );
        response = await apiService.testHybrid(
          hybridId || "temp",
          input,
          context,
        );
      }

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      if (response.success && response.data) {
        const testResultData: HybridExecutionContext = {
          ...response.data,
          responseTime,
          timestamp: new Date().toISOString(),
        } as any;

        // Ensure all required fields are present
        if (!testResultData.hybridMetadata) {
          testResultData.hybridMetadata = {
            orchestrationMode: validatedConfig.orchestration.mode,
            totalToolsExecuted: 0,
            successfulTools: 0,
            failedTools: 0,
            skippedTools: 0,
            adaptiveBehaviorTriggered: false,
            fallbackUsed: false,
            contextPropagationSize: JSON.stringify(context).length,
            performanceMetrics: {
              totalLatency: responseTime,
              agentReasoningTime: 0,
              toolExecutionTime: 0,
              contextPropagationTime: 0,
              memoryUsage: 0,
              cpuUsage: 0,
            },
          };
        }

        if (!testResultData.results) {
          testResultData.results = {
            success: response.success,
            output: response.data,
            confidence: 0.8,
            reasoning: "Hybrid execution completed successfully",
            toolResults: {},
          };
        }

        setTestResult(testResultData);

        toast({
          title: "Test Successful",
          description: `Hybrid executed successfully in ${responseTime}ms with ${testResultData.hybridMetadata.totalToolsExecuted} tools`,
        });

        // Update session memory with comprehensive test result
        stateManager.addToMemory(sessionId, {
          type: "hybrid_test",
          hybridId: hybridId,
          hybridName: hybridConfig.name,
          input,
          output: testResultData,
          timestamp: new Date(),
          metadata: {
            responseTime,
            orchestrationMode: validatedConfig.orchestration.mode,
            toolsConfigured: validatedConfig.toolConfigs.length,
            toolsExecuted: testResultData.hybridMetadata.totalToolsExecuted,
            successfulTools: testResultData.hybridMetadata.successfulTools,
            failedTools: testResultData.hybridMetadata.failedTools,
            agentReasoningTime:
              testResultData.hybridMetadata.performanceMetrics
                .agentReasoningTime,
            toolExecutionTime:
              testResultData.hybridMetadata.performanceMetrics
                .toolExecutionTime,
            fallbackUsed: testResultData.hybridMetadata.fallbackUsed,
            adaptiveBehaviorTriggered:
              testResultData.hybridMetadata.adaptiveBehaviorTriggered,
            securityEnabled: validatedConfig.security.accessControl.enabled,
            monitoringEnabled: validatedConfig.monitoring.enabled,
            confidence: testResultData.results.confidence,
          },
        });

        // Update analytics if available
        if (hybridId) {
          loadAnalytics();
          loadExecutionHistory();
        }
      } else {
        throw new Error(
          response.error || "Test failed - no valid response received",
        );
      }
    } catch (error: any) {
      console.error("Hybrid test failed:", error);

      const failedResult: HybridExecutionContext = {
        id: "test_failed",
        hybridId: hybridId || "temp",
        sessionId,
        startTime: new Date(),
        endTime: new Date(),
        status: "failed",
        error: error.message,
        errorCode: error.code || "HYBRID_TEST_FAILED",
        stackTrace: error.stack,
        toolExecutions: [],
        hybridMetadata: {
          orchestrationMode: hybridConfig.orchestration.mode,
          totalToolsExecuted: 0,
          successfulTools: 0,
          failedTools: 0,
          skippedTools: hybridConfig.toolConfigs.length,
          adaptiveBehaviorTriggered: false,
          fallbackUsed: false,
          contextPropagationSize: 0,
          performanceMetrics: {
            totalLatency: 0,
            agentReasoningTime: 0,
            toolExecutionTime: 0,
            contextPropagationTime: 0,
            memoryUsage: 0,
            cpuUsage: 0,
          },
        },
        results: {
          success: false,
          output: null,
          confidence: 0,
          reasoning: `Hybrid test failed: ${error.message}`,
          toolResults: {},
        },
        events: [
          {
            id: `error_${Date.now()}`,
            timestamp: new Date(),
            type: "test_failed",
            source: "system",
            message: error.message,
            severity: "error",
          },
        ],
      };

      setTestResult(failedResult);

      toast({
        title: "Test Failed",
        description: error.message || "Hybrid test failed",
        variant: "destructive",
      });

      // Log failed test to session memory
      stateManager.addToMemory(sessionId, {
        type: "hybrid_test_failed",
        hybridId: hybridId,
        hybridName: hybridConfig.name,
        error: error.message,
        timestamp: new Date(),
        metadata: {
          configurationAttempted: {
            orchestrationMode: hybridConfig.orchestration.mode,
            toolCount: hybridConfig.toolConfigs.length,
            agentConfigured: !!(
              hybridConfig.agentConfig.inline ||
              hybridConfig.agentConfig.agentId
            ),
            securityEnabled: hybridConfig.security.accessControl.enabled,
            monitoringEnabled: hybridConfig.monitoring.enabled,
          },
          errorType: error.name,
          errorCode: error.code,
          inputProvided: testInput,
        },
      });
    } finally {
      setTesting(false);
    }
  };

  const handleDeploy = async () => {
    try {
      setDeploying(true);
      setDeploymentStatus("deploying");

      // Validate configuration first
      const validatedConfig = ToolAgentHybridConfigSchema.parse(hybridConfig);

      if (!hybridId) {
        throw new Error("Hybrid must be saved before deployment");
      }

      const response = await apiService.deployHybrid(
        hybridId,
        hybridConfig.deployment.environment,
      );

      if (response.success) {
        setDeploymentStatus("deployed");

        toast({
          title: "Deployment Successful",
          description: `Hybrid deployed at: ${response.data?.endpoint}`,
        });

        // Update session state with deployment info
        stateManager.updateContext(sessionId, {
          deploymentInfo: {
            deploymentId: response.data?.deploymentId,
            endpoint: response.data?.endpoint,
            deployedAt: new Date(),
            status: "active",
          },
        });

        onDeploy(hybridConfig);
      } else {
        setDeploymentStatus("failed");
        throw new Error(response.error || "Deployment failed");
      }
    } catch (error: any) {
      console.error("Deployment failed:", error);
      setDeploymentStatus("failed");

      toast({
        title: "Deployment Failed",
        description: error.message || "Failed to deploy hybrid",
        variant: "destructive",
      });
    } finally {
      setDeploying(false);
    }
  };

  const addToolConfig = () => {
    const newToolConfig = {
      priority: hybridConfig.toolConfigs.length + 1,
      conditions: {
        enabled: true,
        triggerRules: [],
        contextRequirements: {},
        executionOrder: hybridConfig.toolConfigs.length,
      },
    };
    updateConfig("toolConfigs", [...hybridConfig.toolConfigs, newToolConfig]);
  };

  const removeToolConfig = (index: number) => {
    const newToolConfigs = hybridConfig.toolConfigs.filter(
      (_, i) => i !== index,
    );
    updateConfig("toolConfigs", newToolConfigs);
  };

  const moveToolConfig = (index: number, direction: "up" | "down") => {
    const newToolConfigs = [...hybridConfig.toolConfigs];
    const targetIndex = direction === "up" ? index - 1 : index + 1;

    if (targetIndex >= 0 && targetIndex < newToolConfigs.length) {
      [newToolConfigs[index], newToolConfigs[targetIndex]] = [
        newToolConfigs[targetIndex],
        newToolConfigs[index],
      ];
      updateConfig("toolConfigs", newToolConfigs);
    }
  };

  const getToolById = (toolId: string) => {
    return availableTools.find((tool) => tool.id === toolId);
  };

  const getAgentById = (agentId: string) => {
    return availableAgents.find((agent) => agent.id === agentId);
  };

  return (
    <div className="flex flex-col h-full w-full bg-background p-6 gap-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Hybrid Builder</h1>
          <p className="text-muted-foreground">
            Create and configure Tool-Agent Hybrid workflows with advanced
            orchestration
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* Connection Status */}
          <div className="flex items-center gap-2 mr-4">
            {wsConnected ? (
              <div className="flex items-center gap-1 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-xs">Connected</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-xs">Disconnected</span>
              </div>
            )}

            {lastSaved && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span className="text-xs">
                  Saved {lastSaved.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>

          <Button
            variant="outline"
            onClick={handleTest}
            disabled={testing || validationErrors.length > 0}
          >
            {testing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Play className="mr-2 h-4 w-4" />
            )}
            {testing ? "Testing..." : "Test"}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleSave(false)}
            disabled={loading || validationErrors.length > 0}
          >
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            {loading ? "Saving..." : "Save"}
          </Button>
          <Button
            onClick={handleDeploy}
            disabled={deploying || !hybridId || validationErrors.length > 0}
            className={
              deploymentStatus === "deployed"
                ? "bg-green-600 hover:bg-green-700"
                : ""
            }
          >
            {deploying ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : deploymentStatus === "deployed" ? (
              <CheckCircle2 className="mr-2 h-4 w-4" />
            ) : (
              <Check className="mr-2 h-4 w-4" />
            )}
            {deploying
              ? "Deploying..."
              : deploymentStatus === "deployed"
                ? "Deployed"
                : "Deploy"}
          </Button>
        </div>
      </div>

      <div className="flex gap-6 h-full">
        {/* Configuration Panel */}
        <div className="w-2/3">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-6 mb-6">
              <TabsTrigger value="composition">Composition</TabsTrigger>
              <TabsTrigger value="orchestration">Orchestration</TabsTrigger>
              <TabsTrigger value="reasoning">Reasoning</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
              <TabsTrigger value="deployment">Deployment</TabsTrigger>
            </TabsList>

            <TabsContent value="composition" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Properties</CardTitle>
                  <CardDescription>
                    Configure the basic properties of your hybrid workflow
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={hybridConfig.name}
                        onChange={(e) => updateConfig("name", e.target.value)}
                        placeholder="My Hybrid Workflow"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="version">Version</Label>
                      <Input
                        id="version"
                        value={hybridConfig.version}
                        onChange={(e) =>
                          updateConfig("version", e.target.value)
                        }
                        placeholder="1.0.0"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={hybridConfig.description}
                      onChange={(e) =>
                        updateConfig("description", e.target.value)
                      }
                      placeholder="Describe what this hybrid workflow does"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={hybridConfig.metadata.category}
                        onValueChange={(value: any) =>
                          updateConfig("metadata.category", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="automation">Automation</SelectItem>
                          <SelectItem value="analysis">Analysis</SelectItem>
                          <SelectItem value="communication">
                            Communication
                          </SelectItem>
                          <SelectItem value="integration">
                            Integration
                          </SelectItem>
                          <SelectItem value="workflow">Workflow</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={hybridConfig.metadata.status}
                        onValueChange={(value: any) =>
                          updateConfig("metadata.status", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="deprecated">Deprecated</SelectItem>
                          <SelectItem value="archived">Archived</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="h-5 w-5" />
                    Agent Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure the AI agent that will orchestrate tool execution
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Agent Source</Label>
                    <div className="flex gap-2">
                      <Button
                        variant={
                          hybridConfig.agentConfig.agentId
                            ? "outline"
                            : "default"
                        }
                        size="sm"
                        onClick={() => {
                          updateConfig("agentConfig", {
                            inline: hybridConfig.agentConfig.inline || {
                              name: "Hybrid Agent",
                              description: "Agent for hybrid execution",
                              model: "gpt-4o",
                              temperature: 0.7,
                              maxTokens: 4000,
                              stateManagement: {
                                enabled: true,
                                persistenceType: "redis",
                                ttl: 3600,
                              },
                              memory: {
                                enabled: true,
                                type: "conversational",
                                contextWindow: 10,
                              },
                              reasoning: {
                                enabled: true,
                                type: "chain-of-thought",
                                steps: 3,
                              },
                              hitl: {
                                enabled: false,
                                mode: "optional",
                                timeout: 300,
                                fallbackStrategy: "none",
                                tags: [],
                              },
                              providers: [],
                            },
                          });
                        }}
                      >
                        <Code className="h-4 w-4 mr-1" />
                        Inline
                      </Button>
                      <Button
                        variant={
                          hybridConfig.agentConfig.agentId
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          updateConfig("agentConfig", { agentId: "" });
                        }}
                      >
                        <Link className="h-4 w-4 mr-1" />
                        Reference
                      </Button>
                    </div>
                  </div>

                  {hybridConfig.agentConfig.agentId !== undefined ? (
                    <div className="space-y-2">
                      <Label htmlFor="agentId">Select Agent</Label>
                      <Select
                        value={hybridConfig.agentConfig.agentId || ""}
                        onValueChange={(value) =>
                          updateConfig("agentConfig.agentId", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select an existing agent" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableAgents.map((agent) => (
                            <SelectItem key={agent.id} value={agent.id!}>
                              {agent.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {hybridConfig.agentConfig.agentId && (
                        <div className="p-3 bg-muted rounded-lg">
                          {(() => {
                            const agent = getAgentById(
                              hybridConfig.agentConfig.agentId!,
                            );
                            return agent ? (
                              <div className="space-y-2">
                                <div className="font-medium">{agent.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {agent.description}
                                </div>
                                <div className="flex gap-2">
                                  <Badge variant="outline">{agent.model}</Badge>
                                  <Badge variant="outline">
                                    Temp: {agent.temperature}
                                  </Badge>
                                  {agent.reasoning.enabled && (
                                    <Badge variant="outline">
                                      <Brain className="h-3 w-3 mr-1" />
                                      {agent.reasoning.type}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="text-sm text-muted-foreground">
                                Agent not found
                              </div>
                            );
                          })()}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4 p-4 border rounded-lg">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Agent Name</Label>
                          <Input
                            value={hybridConfig.agentConfig.inline?.name || ""}
                            onChange={(e) =>
                              updateConfig(
                                "agentConfig.inline.name",
                                e.target.value,
                              )
                            }
                            placeholder="Hybrid Agent"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Model</Label>
                          <Select
                            value={
                              hybridConfig.agentConfig.inline?.model || "gpt-4o"
                            }
                            onValueChange={(value) =>
                              updateConfig("agentConfig.inline.model", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                              <SelectItem value="gpt-4-turbo">
                                GPT-4 Turbo
                              </SelectItem>
                              <SelectItem value="claude-3-5-sonnet-20241022">
                                Claude 3.5 Sonnet
                              </SelectItem>
                              <SelectItem value="claude-3-opus-20240229">
                                Claude 3 Opus
                              </SelectItem>
                              <SelectItem value="gemini-1.5-pro">
                                Gemini 1.5 Pro
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>Description</Label>
                        <Textarea
                          value={
                            hybridConfig.agentConfig.inline?.description || ""
                          }
                          onChange={(e) =>
                            updateConfig(
                              "agentConfig.inline.description",
                              e.target.value,
                            )
                          }
                          placeholder="Agent for hybrid execution"
                          rows={2}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>
                            Temperature (
                            {hybridConfig.agentConfig.inline?.temperature ||
                              0.7}
                            )
                          </Label>
                          <Slider
                            value={[
                              hybridConfig.agentConfig.inline?.temperature ||
                                0.7,
                            ]}
                            onValueChange={(value) =>
                              updateConfig(
                                "agentConfig.inline.temperature",
                                value[0],
                              )
                            }
                            min={0}
                            max={1}
                            step={0.1}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Max Tokens</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.agentConfig.inline?.maxTokens || 4000
                            }
                            onChange={(e) =>
                              updateConfig(
                                "agentConfig.inline.maxTokens",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wrench className="h-5 w-5" />
                    Tool Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure the tools that will be available to the agent
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <Label>Tools ({hybridConfig.toolConfigs.length})</Label>
                    <Button onClick={addToolConfig} size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      Add Tool
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {hybridConfig.toolConfigs.map((toolConfig, index) => (
                      <div
                        key={index}
                        className="p-4 border rounded-lg space-y-3"
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">#{index + 1}</Badge>
                            <Label>Tool Configuration</Label>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => moveToolConfig(index, "up")}
                              disabled={index === 0}
                            >
                              <ArrowUp className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => moveToolConfig(index, "down")}
                              disabled={
                                index === hybridConfig.toolConfigs.length - 1
                              }
                            >
                              <ArrowDown className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeToolConfig(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <Label>Tool Source</Label>
                          <div className="flex gap-2">
                            <Button
                              variant={
                                toolConfig.toolId ? "outline" : "default"
                              }
                              size="sm"
                              onClick={() => {
                                const newToolConfigs = [
                                  ...hybridConfig.toolConfigs,
                                ];
                                newToolConfigs[index] = {
                                  ...toolConfig,
                                  inline: {
                                    name: "New Tool",
                                    inputSchema: JSON.stringify(
                                      { type: "object", properties: {} },
                                      null,
                                      2,
                                    ),
                                    outputSchema: JSON.stringify(
                                      { type: "object", properties: {} },
                                      null,
                                      2,
                                    ),
                                    validationRules: "",
                                    endpoint: "",
                                    method: "POST",
                                    timeout: 30000,
                                    retryAttempts: 3,
                                    hitl: {
                                      enabled: false,
                                      mode: "optional",
                                      timeout: 300,
                                      fallbackStrategy: "none",
                                      parameterBinding: {
                                        sessionContext: false,
                                        userContext: false,
                                        agentMemory: false,
                                      },
                                    },
                                  },
                                };
                                delete newToolConfigs[index].toolId;
                                updateConfig("toolConfigs", newToolConfigs);
                              }}
                            >
                              <Code className="h-4 w-4 mr-1" />
                              Inline
                            </Button>
                            <Button
                              variant={
                                toolConfig.toolId ? "default" : "outline"
                              }
                              size="sm"
                              onClick={() => {
                                const newToolConfigs = [
                                  ...hybridConfig.toolConfigs,
                                ];
                                newToolConfigs[index] = {
                                  ...toolConfig,
                                  toolId: "",
                                };
                                delete newToolConfigs[index].inline;
                                updateConfig("toolConfigs", newToolConfigs);
                              }}
                            >
                              <Link className="h-4 w-4 mr-1" />
                              Reference
                            </Button>
                          </div>
                        </div>

                        {toolConfig.toolId !== undefined ? (
                          <div className="space-y-2">
                            <Label>Select Tool</Label>
                            <Select
                              value={toolConfig.toolId || ""}
                              onValueChange={(value) => {
                                const newToolConfigs = [
                                  ...hybridConfig.toolConfigs,
                                ];
                                newToolConfigs[index] = {
                                  ...toolConfig,
                                  toolId: value,
                                };
                                updateConfig("toolConfigs", newToolConfigs);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select an existing tool" />
                              </SelectTrigger>
                              <SelectContent>
                                {availableTools.map((tool) => (
                                  <SelectItem key={tool.id} value={tool.id!}>
                                    {tool.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {toolConfig.toolId && (
                              <div className="p-3 bg-muted rounded-lg">
                                {(() => {
                                  const tool = getToolById(toolConfig.toolId!);
                                  return tool ? (
                                    <div className="space-y-2">
                                      <div className="font-medium">
                                        {tool.name}
                                      </div>
                                      <div className="text-sm text-muted-foreground">
                                        {tool.description}
                                      </div>
                                      <div className="flex gap-2">
                                        <Badge variant="outline">
                                          {tool.method}
                                        </Badge>
                                        <Badge variant="outline">
                                          {tool.timeout}ms
                                        </Badge>
                                        {tool.endpoint && (
                                          <Badge variant="outline">
                                            <Network className="h-3 w-3 mr-1" />
                                            API
                                          </Badge>
                                        )}
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="text-sm text-muted-foreground">
                                      Tool not found
                                    </div>
                                  );
                                })()}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="space-y-4 p-3 border rounded">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Tool Name</Label>
                                <Input
                                  value={toolConfig.inline?.name || ""}
                                  onChange={(e) => {
                                    const newToolConfigs = [
                                      ...hybridConfig.toolConfigs,
                                    ];
                                    newToolConfigs[index] = {
                                      ...toolConfig,
                                      inline: {
                                        ...toolConfig.inline,
                                        name: e.target.value,
                                      },
                                    };
                                    updateConfig("toolConfigs", newToolConfigs);
                                  }}
                                  placeholder="Tool Name"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Endpoint</Label>
                                <Input
                                  value={toolConfig.inline?.endpoint || ""}
                                  onChange={(e) => {
                                    const newToolConfigs = [
                                      ...hybridConfig.toolConfigs,
                                    ];
                                    newToolConfigs[index] = {
                                      ...toolConfig,
                                      inline: {
                                        ...toolConfig.inline,
                                        endpoint: e.target.value,
                                      },
                                    };
                                    updateConfig("toolConfigs", newToolConfigs);
                                  }}
                                  placeholder="https://api.example.com/endpoint"
                                />
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Priority</Label>
                            <Input
                              type="number"
                              value={toolConfig.priority}
                              onChange={(e) => {
                                const newToolConfigs = [
                                  ...hybridConfig.toolConfigs,
                                ];
                                newToolConfigs[index] = {
                                  ...toolConfig,
                                  priority: parseInt(e.target.value),
                                };
                                updateConfig("toolConfigs", newToolConfigs);
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Execution Order</Label>
                            <Input
                              type="number"
                              value={toolConfig.conditions?.executionOrder || 0}
                              onChange={(e) => {
                                const newToolConfigs = [
                                  ...hybridConfig.toolConfigs,
                                ];
                                newToolConfigs[index] = {
                                  ...toolConfig,
                                  conditions: {
                                    ...toolConfig.conditions,
                                    executionOrder: parseInt(e.target.value),
                                  },
                                };
                                updateConfig("toolConfigs", newToolConfigs);
                              }}
                            />
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <Label>Enabled</Label>
                          <Switch
                            checked={toolConfig.conditions?.enabled !== false}
                            onCheckedChange={(checked) => {
                              const newToolConfigs = [
                                ...hybridConfig.toolConfigs,
                              ];
                              newToolConfigs[index] = {
                                ...toolConfig,
                                conditions: {
                                  ...toolConfig.conditions,
                                  enabled: checked,
                                },
                              };
                              updateConfig("toolConfigs", newToolConfigs);
                            }}
                          />
                        </div>
                      </div>
                    ))}

                    {hybridConfig.toolConfigs.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Wrench className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No tools configured</p>
                        <p className="text-xs">
                          Add tools to enable hybrid execution
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="orchestration" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Workflow className="h-5 w-5" />
                    Orchestration Settings
                  </CardTitle>
                  <CardDescription>
                    Configure how tools are orchestrated and executed
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Orchestration Mode</Label>
                      <Select
                        value={hybridConfig.orchestration.mode}
                        onValueChange={(value: any) =>
                          updateConfig("orchestration.mode", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sequential">Sequential</SelectItem>
                          <SelectItem value="parallel">Parallel</SelectItem>
                          <SelectItem value="conditional">
                            Conditional
                          </SelectItem>
                          <SelectItem value="dynamic">Dynamic</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Max Concurrent Tools</Label>
                      <Input
                        type="number"
                        value={hybridConfig.orchestration.maxConcurrentTools}
                        onChange={(e) =>
                          updateConfig(
                            "orchestration.maxConcurrentTools",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Timeout (ms)</Label>
                      <Input
                        type="number"
                        value={hybridConfig.orchestration.timeoutMs}
                        onChange={(e) =>
                          updateConfig(
                            "orchestration.timeoutMs",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Fallback Strategy</Label>
                      <Select
                        value={hybridConfig.orchestration.fallbackStrategy}
                        onValueChange={(value: any) =>
                          updateConfig("orchestration.fallbackStrategy", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="agent_only">Agent Only</SelectItem>
                          <SelectItem value="tools_only">Tools Only</SelectItem>
                          <SelectItem value="best_effort">
                            Best Effort
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Retry Strategy</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Retry</Label>
                      <Switch
                        checked={
                          hybridConfig.orchestration.retryStrategy.enabled
                        }
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "orchestration.retryStrategy.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.orchestration.retryStrategy.enabled && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Max Attempts</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.orchestration.retryStrategy
                                .maxAttempts
                            }
                            onChange={(e) =>
                              updateConfig(
                                "orchestration.retryStrategy.maxAttempts",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Backoff Multiplier</Label>
                          <Input
                            type="number"
                            step="0.1"
                            value={
                              hybridConfig.orchestration.retryStrategy
                                .backoffMultiplier
                            }
                            onChange={(e) =>
                              updateConfig(
                                "orchestration.retryStrategy.backoffMultiplier",
                                parseFloat(e.target.value),
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reasoning" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    Reasoning Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure how the agent reasons about tool selection and
                    execution
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Tool Selection Strategy</Label>
                    <Select
                      value={hybridConfig.reasoning.toolSelectionStrategy}
                      onValueChange={(value: any) =>
                        updateConfig("reasoning.toolSelectionStrategy", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="agent_driven">
                          Agent Driven
                        </SelectItem>
                        <SelectItem value="rule_based">Rule Based</SelectItem>
                        <SelectItem value="ml_optimized">
                          ML Optimized
                        </SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Context Propagation</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Context Propagation</Label>
                      <Switch
                        checked={
                          hybridConfig.reasoning.contextPropagation.enabled
                        }
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "reasoning.contextPropagation.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.reasoning.contextPropagation.enabled && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label>Include Tool Results</Label>
                          <Switch
                            checked={
                              hybridConfig.reasoning.contextPropagation
                                .includeToolResults
                            }
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "reasoning.contextPropagation.includeToolResults",
                                checked,
                              )
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>Include Agent Memory</Label>
                          <Switch
                            checked={
                              hybridConfig.reasoning.contextPropagation
                                .includeAgentMemory
                            }
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "reasoning.contextPropagation.includeAgentMemory",
                                checked,
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Max Context Size</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.reasoning.contextPropagation
                                .maxContextSize
                            }
                            onChange={(e) =>
                              updateConfig(
                                "reasoning.contextPropagation.maxContextSize",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Adaptive Behavior</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Adaptive Behavior</Label>
                      <Switch
                        checked={
                          hybridConfig.reasoning.adaptiveBehavior.enabled
                        }
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "reasoning.adaptiveBehavior.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.reasoning.adaptiveBehavior.enabled && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>
                            Learning Rate (
                            {
                              hybridConfig.reasoning.adaptiveBehavior
                                .learningRate
                            }
                            )
                          </Label>
                          <Slider
                            value={[
                              hybridConfig.reasoning.adaptiveBehavior
                                .learningRate,
                            ]}
                            onValueChange={(value) =>
                              updateConfig(
                                "reasoning.adaptiveBehavior.learningRate",
                                value[0],
                              )
                            }
                            min={0.01}
                            max={1.0}
                            step={0.01}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>
                            Performance Threshold (
                            {
                              hybridConfig.reasoning.adaptiveBehavior
                                .performanceThreshold
                            }
                            )
                          </Label>
                          <Slider
                            value={[
                              hybridConfig.reasoning.adaptiveBehavior
                                .performanceThreshold,
                            ]}
                            onValueChange={(value) =>
                              updateConfig(
                                "reasoning.adaptiveBehavior.performanceThreshold",
                                value[0],
                              )
                            }
                            min={0.1}
                            max={1.0}
                            step={0.1}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Security Settings
                  </CardTitle>
                  <CardDescription>
                    Configure security, access control, and data isolation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">Access Control</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Access Control</Label>
                      <Switch
                        checked={hybridConfig.security.accessControl.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "security.accessControl.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.security.accessControl.enabled && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label>Role-Based Access</Label>
                          <Switch
                            checked={
                              hybridConfig.security.accessControl
                                .roleBasedAccess
                            }
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "security.accessControl.roleBasedAccess",
                                checked,
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Data Isolation</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Data Isolation</Label>
                      <Switch
                        checked={hybridConfig.security.dataIsolation.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "security.dataIsolation.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.security.dataIsolation.enabled && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label>Encrypt Sensitive Data</Label>
                          <Switch
                            checked={
                              hybridConfig.security.dataIsolation
                                .encryptSensitiveData
                            }
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "security.dataIsolation.encryptSensitiveData",
                                checked,
                              )
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>Audit Trail</Label>
                          <Switch
                            checked={
                              hybridConfig.security.dataIsolation.auditTrail
                            }
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "security.dataIsolation.auditTrail",
                                checked,
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Rate Limiting</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Rate Limiting</Label>
                      <Switch
                        checked={hybridConfig.security.rateLimiting.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig("security.rateLimiting.enabled", checked)
                        }
                      />
                    </div>
                    {hybridConfig.security.rateLimiting.enabled && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Requests Per Minute</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.security.rateLimiting
                                .requestsPerMinute
                            }
                            onChange={(e) =>
                              updateConfig(
                                "security.rateLimiting.requestsPerMinute",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Burst Limit</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.security.rateLimiting.burstLimit
                            }
                            onChange={(e) =>
                              updateConfig(
                                "security.rateLimiting.burstLimit",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="monitoring" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Monitoring & Observability
                  </CardTitle>
                  <CardDescription>
                    Configure monitoring, analytics, and alerting
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <Label>Enable Monitoring</Label>
                    <Switch
                      checked={hybridConfig.monitoring.enabled}
                      onCheckedChange={(checked) =>
                        updateConfig("monitoring.enabled", checked)
                      }
                    />
                  </div>

                  {hybridConfig.monitoring.enabled && (
                    <>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="flex items-center justify-between">
                          <Label>Track Performance</Label>
                          <Switch
                            checked={hybridConfig.monitoring.trackPerformance}
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "monitoring.trackPerformance",
                                checked,
                              )
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>Track Tool Usage</Label>
                          <Switch
                            checked={hybridConfig.monitoring.trackToolUsage}
                            onCheckedChange={(checked) =>
                              updateConfig("monitoring.trackToolUsage", checked)
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>Track Agent Decisions</Label>
                          <Switch
                            checked={
                              hybridConfig.monitoring.trackAgentDecisions
                            }
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "monitoring.trackAgentDecisions",
                                checked,
                              )
                            }
                          />
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-4">
                        <h4 className="font-medium">Alerting</h4>
                        <div className="flex items-center justify-between">
                          <Label>Enable Alerting</Label>
                          <Switch
                            checked={hybridConfig.monitoring.alerting.enabled}
                            onCheckedChange={(checked) =>
                              updateConfig(
                                "monitoring.alerting.enabled",
                                checked,
                              )
                            }
                          />
                        </div>
                        {hybridConfig.monitoring.alerting.enabled && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>
                                  Error Threshold (
                                  {
                                    hybridConfig.monitoring.alerting
                                      .errorThreshold
                                  }
                                  )
                                </Label>
                                <Slider
                                  value={[
                                    hybridConfig.monitoring.alerting
                                      .errorThreshold,
                                  ]}
                                  onValueChange={(value) =>
                                    updateConfig(
                                      "monitoring.alerting.errorThreshold",
                                      value[0],
                                    )
                                  }
                                  min={0.01}
                                  max={1.0}
                                  step={0.01}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Latency Threshold (ms)</Label>
                                <Input
                                  type="number"
                                  value={
                                    hybridConfig.monitoring.alerting
                                      .latencyThreshold
                                  }
                                  onChange={(e) =>
                                    updateConfig(
                                      "monitoring.alerting.latencyThreshold",
                                      parseInt(e.target.value),
                                    )
                                  }
                                />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label>Webhook URL</Label>
                              <Input
                                value={
                                  hybridConfig.monitoring.alerting.webhookUrl ||
                                  ""
                                }
                                onChange={(e) =>
                                  updateConfig(
                                    "monitoring.alerting.webhookUrl",
                                    e.target.value,
                                  )
                                }
                                placeholder="https://hooks.slack.com/..."
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </>
                  )}

                  {analytics && (
                    <>
                      <Separator />
                      <div className="space-y-4">
                        <h4 className="font-medium">Current Analytics</h4>
                        <div className="grid grid-cols-4 gap-4">
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold">
                              {analytics.execution.totalExecutions}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Total Executions
                            </div>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">
                              {analytics.execution.successfulExecutions}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Successful
                            </div>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-red-600">
                              {analytics.execution.failedExecutions}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Failed
                            </div>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold">
                              {analytics.execution.averageExecutionTime.toFixed(
                                0,
                              )}
                              ms
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Avg Time
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="deployment" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Deployment Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure deployment settings, scaling, and health checks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label>Environment</Label>
                    <Select
                      value={hybridConfig.deployment.environment}
                      onValueChange={(value: any) =>
                        updateConfig("deployment.environment", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="development">Development</SelectItem>
                        <SelectItem value="staging">Staging</SelectItem>
                        <SelectItem value="production">Production</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Scaling Policy</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Auto Scaling</Label>
                      <Switch
                        checked={hybridConfig.deployment.scalingPolicy.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "deployment.scalingPolicy.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.deployment.scalingPolicy.enabled && (
                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Min Instances</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.deployment.scalingPolicy.minInstances
                            }
                            onChange={(e) =>
                              updateConfig(
                                "deployment.scalingPolicy.minInstances",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Max Instances</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.deployment.scalingPolicy.maxInstances
                            }
                            onChange={(e) =>
                              updateConfig(
                                "deployment.scalingPolicy.maxInstances",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Target CPU %</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.deployment.scalingPolicy
                                .targetCpuUtilization
                            }
                            onChange={(e) =>
                              updateConfig(
                                "deployment.scalingPolicy.targetCpuUtilization",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Health Check</h4>
                    <div className="flex items-center justify-between">
                      <Label>Enable Health Check</Label>
                      <Switch
                        checked={hybridConfig.deployment.healthCheck.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig(
                            "deployment.healthCheck.enabled",
                            checked,
                          )
                        }
                      />
                    </div>
                    {hybridConfig.deployment.healthCheck.enabled && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Interval (ms)</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.deployment.healthCheck.intervalMs
                            }
                            onChange={(e) =>
                              updateConfig(
                                "deployment.healthCheck.intervalMs",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Timeout (ms)</Label>
                          <Input
                            type="number"
                            value={
                              hybridConfig.deployment.healthCheck.timeoutMs
                            }
                            onChange={(e) =>
                              updateConfig(
                                "deployment.healthCheck.timeoutMs",
                                parseInt(e.target.value),
                              )
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Deployment Status</h4>
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          deploymentStatus === "deployed"
                            ? "bg-green-500"
                            : deploymentStatus === "deploying"
                              ? "bg-yellow-500 animate-pulse"
                              : deploymentStatus === "failed"
                                ? "bg-red-500"
                                : "bg-gray-400"
                        }`}
                      />
                      <span className="text-sm font-medium capitalize">
                        {deploymentStatus === "idle"
                          ? "Not Deployed"
                          : deploymentStatus}
                      </span>
                      {deploymentStatus === "deploying" && (
                        <Progress value={75} className="w-24 h-2" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview Panel */}
        <div className="w-1/3">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Code className="mr-2 h-5 w-5" />
                Configuration Preview
              </CardTitle>
              <CardDescription>
                Real-time preview of your hybrid configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Workflow className="h-3 w-3" />
                    {hybridConfig.orchestration.mode}
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Wrench className="h-3 w-3" />
                    {hybridConfig.toolConfigs.length} Tools
                  </Badge>
                  {hybridConfig.agentConfig.inline && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <Bot className="h-3 w-3" />
                      {hybridConfig.agentConfig.inline.model}
                    </Badge>
                  )}
                  {hybridConfig.security.accessControl.enabled && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <Shield className="h-3 w-3" />
                      Secured
                    </Badge>
                  )}
                  {hybridConfig.monitoring.enabled && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <Activity className="h-3 w-3" />
                      Monitored
                    </Badge>
                  )}
                </div>

                {validationErrors.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-destructive">
                      Validation Errors:
                    </h4>
                    <div className="space-y-1">
                      {validationErrors.map((error, index) => (
                        <div
                          key={index}
                          className="text-xs text-destructive bg-destructive/10 p-2 rounded"
                        >
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {testResult && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium flex items-center gap-2">
                      Test Result:
                      {testResult.error ? (
                        <Badge variant="destructive" className="text-xs">
                          <XCircle className="h-3 w-3 mr-1" />
                          Failed
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="text-xs bg-green-50 text-green-700 border-green-200"
                        >
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          Success
                        </Badge>
                      )}
                    </h4>

                    {!testResult.error && (
                      <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                        <span className="flex items-center gap-1">
                          <Timer className="h-3 w-3" />
                          {
                            testResult.hybridMetadata.performanceMetrics
                              .totalLatency
                          }
                          ms
                        </span>
                        <span className="flex items-center gap-1">
                          <Wrench className="h-3 w-3" />
                          {testResult.hybridMetadata.totalToolsExecuted} tools
                        </span>
                        <span className="flex items-center gap-1">
                          <Brain className="h-3 w-3" />
                          {
                            testResult.hybridMetadata.performanceMetrics
                              .agentReasoningTime
                          }
                          ms reasoning
                        </span>
                      </div>
                    )}

                    <div className="text-xs bg-muted p-3 rounded border max-h-[300px] overflow-auto">
                      <pre>{JSON.stringify(testResult.results, null, 2)}</pre>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm">Test Input</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        setTestInput(
                          JSON.stringify(
                            { message: "Hello, test the hybrid workflow" },
                            null,
                            2,
                          ),
                        )
                      }
                    >
                      <FileText className="h-3 w-3 mr-1" />
                      Sample
                    </Button>
                  </div>
                  <Textarea
                    value={testInput}
                    onChange={(e) => setTestInput(e.target.value)}
                    className="font-mono text-xs"
                    rows={4}
                    placeholder="Enter test input as JSON"
                  />
                </div>

                <ScrollArea className="h-[400px] rounded-md border p-4 bg-muted/50">
                  <pre className="text-xs">{previewJson}</pre>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HybridBuilder;
