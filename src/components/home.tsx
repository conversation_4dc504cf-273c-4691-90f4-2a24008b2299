import React from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowRight,
  BarChart3,
  Bot,
  Code,
  Cog,
  Cpu,
  Database,
  Globe,
  Layers,
  LayoutDashboard,
  Settings,
  Wrench,
  Zap,
} from "lucide-react";
import Sidebar from "./Dashboard/Sidebar";

const Home = () => {
  // Mock data for recently used components
  const recentlyUsed = [
    {
      id: 1,
      name: "Customer Support Agent",
      type: "Agent",
      lastUsed: "2 hours ago",
    },
    {
      id: 2,
      name: "Data Extraction Tool",
      type: "Tool",
      lastUsed: "1 day ago",
    },
    {
      id: 3,
      name: "Product Recommendation Agent",
      type: "Agent",
      lastUsed: "3 days ago",
    },
  ];

  // Mock data for summary metrics
  const summaryMetrics = [
    {
      title: "Active Agents",
      value: "12",
      icon: <Bot className="h-5 w-5 text-blue-500" />,
    },
    {
      title: "Available Tools",
      value: "24",
      icon: <Wrench className="h-5 w-5 text-green-500" />,
    },
    {
      title: "API Calls Today",
      value: "1,458",
      icon: <Zap className="h-5 w-5 text-yellow-500" />,
    },
    {
      title: "Avg. Response Time",
      value: "1.2s",
      icon: <BarChart3 className="h-5 w-5 text-purple-500" />,
    },
  ];

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
      },
    },
  };

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex-1 overflow-auto p-6">
        <header className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome to SynapseAI
          </h1>
          <p className="text-muted-foreground mt-2">
            Universal AI Orchestration Platform
          </p>
        </header>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {summaryMetrics.map((metric, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card>
                <CardContent className="p-6 flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {metric.title}
                    </p>
                    <h3 className="text-2xl font-bold mt-1">{metric.value}</h3>
                  </div>
                  <div className="p-2 bg-background rounded-full border">
                    {metric.icon}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Access frequently used platform features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2"
                  onClick={() => (window.location.href = "/agent-builder")}
                >
                  <Bot className="h-6 w-6" />
                  <span>Create New Agent</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2"
                  onClick={() => (window.location.href = "/tool-manager")}
                >
                  <Wrench className="h-6 w-6" />
                  <span>Create New Tool</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2"
                  onClick={() =>
                    (window.location.href = "/provider-integration")
                  }
                >
                  <Cpu className="h-6 w-6" />
                  <span>Configure Providers</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2"
                  onClick={() => (window.location.href = "/deployment")}
                >
                  <Globe className="h-6 w-6" />
                  <span>Deploy Solution</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Platform Status</CardTitle>
              <CardDescription>System health and performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">API Services</span>
                  <span className="text-sm font-medium text-green-500">
                    Operational
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Database</span>
                  <span className="text-sm font-medium text-green-500">
                    Operational
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">WebSocket</span>
                  <span className="text-sm font-medium text-green-500">
                    Operational
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Provider APIs</span>
                  <span className="text-sm font-medium text-green-500">
                    Operational
                  </span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" size="sm" className="w-full">
                View Detailed Status
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Tabs defaultValue="recent">
          <TabsList className="mb-4">
            <TabsTrigger value="recent">Recently Used</TabsTrigger>
            <TabsTrigger value="agents">My Agents</TabsTrigger>
            <TabsTrigger value="tools">My Tools</TabsTrigger>
          </TabsList>

          <TabsContent value="recent">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentlyUsed.map((item) => (
                <Card key={item.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{item.name}</CardTitle>
                      <div className="px-2 py-1 rounded-full bg-muted text-xs font-medium">
                        {item.type}
                      </div>
                    </div>
                    <CardDescription>Last used {item.lastUsed}</CardDescription>
                  </CardHeader>
                  <CardFooter>
                    <Button variant="ghost" size="sm" className="ml-auto">
                      Open <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="agents">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card className="border-dashed border-2 flex flex-col items-center justify-center p-6">
                <Bot className="h-8 w-8 text-muted-foreground mb-2" />
                <h3 className="font-medium mb-1">Create New Agent</h3>
                <p className="text-sm text-muted-foreground text-center mb-4">
                  Build an AI agent with state management and reasoning
                  capabilities
                </p>
                <Button>Get Started</Button>
              </Card>
              {/* Placeholder for actual agents */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer Support Agent</CardTitle>
                  <CardDescription>
                    Handles customer inquiries and support tickets
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button variant="outline" size="sm" className="ml-auto">
                    Edit
                  </Button>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Product Recommendation Agent</CardTitle>
                  <CardDescription>
                    Suggests products based on user preferences
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button variant="outline" size="sm" className="ml-auto">
                    Edit
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tools">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card className="border-dashed border-2 flex flex-col items-center justify-center p-6">
                <Wrench className="h-8 w-8 text-muted-foreground mb-2" />
                <h3 className="font-medium mb-1">Create New Tool</h3>
                <p className="text-sm text-muted-foreground text-center mb-4">
                  Build a stateless task API with input/output schemas
                </p>
                <Button>Get Started</Button>
              </Card>
              {/* Placeholder for actual tools */}
              <Card>
                <CardHeader>
                  <CardTitle>Data Extraction Tool</CardTitle>
                  <CardDescription>
                    Extracts structured data from text inputs
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button variant="outline" size="sm" className="ml-auto">
                    Edit
                  </Button>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Email Sender</CardTitle>
                  <CardDescription>
                    Sends formatted emails to specified recipients
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button variant="outline" size="sm" className="ml-auto">
                    Edit
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Home;
