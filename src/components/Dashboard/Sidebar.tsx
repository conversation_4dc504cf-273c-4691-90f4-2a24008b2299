import React from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Bot,
  Wrench,
  Cloud,
  Radio,
  Shield,
  Rocket,
  Settings,
  LogOut,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  collapsed?: boolean;
}

const NavItem = ({
  to,
  icon,
  label,
  isActive = false,
  collapsed = false,
}: NavItemProps) => {
  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            to={to}
            className={cn(
              "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
              collapsed ? "justify-center" : "",
            )}
          >
            <span className="flex h-5 w-5 items-center justify-center">
              {icon}
            </span>
            {!collapsed && <span>{label}</span>}
          </Link>
        </TooltipTrigger>
        {collapsed && <TooltipContent side="right">{label}</TooltipContent>}
      </Tooltip>
    </TooltipProvider>
  );
};

interface SidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  className?: string;
}

const Sidebar = ({
  collapsed = false,
  onToggleCollapse,
  className,
}: SidebarProps) => {
  // In a real app, you would determine the active route from the router
  const activeRoute = window.location.pathname;

  return (
    <div
      className={cn(
        "flex h-full flex-col border-r bg-background",
        collapsed ? "w-16" : "w-64",
        className,
      )}
    >
      <div className="flex h-14 items-center border-b px-3">
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
            <span className="text-lg font-bold text-primary-foreground">S</span>
          </div>
          {!collapsed && (
            <span className="text-lg font-semibold">SynapseAI</span>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          <NavItem
            to="/"
            icon={<LayoutDashboard className="h-5 w-5" />}
            label="Dashboard"
            isActive={activeRoute === "/"}
            collapsed={collapsed}
          />
          <NavItem
            to="/agent-builder"
            icon={<Bot className="h-5 w-5" />}
            label="Agent Builder"
            isActive={activeRoute.includes("/agent-builder")}
            collapsed={collapsed}
          />
          <NavItem
            to="/tool-manager"
            icon={<Wrench className="h-5 w-5" />}
            label="Tool Manager"
            isActive={activeRoute.includes("/tool-manager")}
            collapsed={collapsed}
          />
          <NavItem
            to="/provider-integration"
            icon={<Cloud className="h-5 w-5" />}
            label="Provider Integration"
            isActive={activeRoute.includes("/provider-integration")}
            collapsed={collapsed}
          />
          <NavItem
            to="/websocket-protocol"
            icon={<Radio className="h-5 w-5" />}
            label="WebSocket Protocol"
            isActive={activeRoute.includes("/websocket-protocol")}
            collapsed={collapsed}
          />
          <NavItem
            to="/security-settings"
            icon={<Shield className="h-5 w-5" />}
            label="Security Settings"
            isActive={activeRoute.includes("/security-settings")}
            collapsed={collapsed}
          />
          <NavItem
            to="/deployment"
            icon={<Rocket className="h-5 w-5" />}
            label="Deployment"
            isActive={activeRoute.includes("/deployment")}
            collapsed={collapsed}
          />
        </nav>
      </div>

      <div className="mt-auto border-t p-2">
        <NavItem
          to="/settings"
          icon={<Settings className="h-5 w-5" />}
          label="Settings"
          isActive={activeRoute.includes("/settings")}
          collapsed={collapsed}
        />
        <NavItem
          to="/logout"
          icon={<LogOut className="h-5 w-5" />}
          label="Logout"
          collapsed={collapsed}
        />
      </div>
    </div>
  );
};

export default Sidebar;
