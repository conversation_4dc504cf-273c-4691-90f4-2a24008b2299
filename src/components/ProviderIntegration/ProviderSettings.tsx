import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Slider } from "@/components/ui/slider";
import {
  AlertCircle,
  Check,
  CheckCircle2,
  XCircle,
  Loader2,
  Plus,
  Trash2,
  Edit,
  Save,
  TestTube,
  Activity,
  Zap,
  Settings,
  Globe,
  Key,
  Clock,
  TrendingUp,
  TrendingDown,
  Wifi,
  WifiOff,
  RefreshCw,
  BarChart3,
  Target,
  Shield,
  Database,
} from "lucide-react";
import { AIProvider } from "@/types/api";
import { apiService } from "@/services/api";
import { useRealTimeData, useWebSocketConnection } from "@/hooks/useRealTimeData";

const ProviderSettings: React.FC = () => {
  const { toast } = useToast();
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [providerMetrics, setProviderMetrics] = useState<Record<string, any>>({});
  const { connected: wsConnected } = useWebSocketConnection();
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Load providers and setup real-time subscriptions
  useEffect(() => {
    const loadProviders = async () => {
      try {
        setLoading(true);
        const response = await apiService.getProviders();
        if (response.success && response.data) {
          setProviders(response.data);
          
          // Load metrics for each provider
          const metrics: Record<string, any> = {};
          await Promise.all(
            response.data.map(async (provider) => {
              try {
                const metricsResponse = await apiService.getProviderMetrics(provider.id);
                if (metricsResponse.success) {
                  metrics[provider.id] = metricsResponse.data;
                }
              } catch (error) {
                console.warn(`Failed to load metrics for provider ${provider.id}:`, error);
              }
            })
          );
          setProviderMetrics(metrics);
        }
      } catch (error) {
        console.error("Failed to load providers:", error);
        toast({
          title: "Error",
          description: "Failed to load AI providers",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadProviders();
  }, [toast]);

  // Real-time metrics updates
  useEffect(() => {
    if (realTimeUpdates && wsConnected) {
      const interval = setInterval(async () => {
        try {
          const response = await apiService.getProviders();
          if (response.success && response.data) {
            setProviders(response.data);
            
            // Update metrics
            const metrics: Record<string, any> = {};
            await Promise.all(
              response.data.map(async (provider) => {
                try {
                  const metricsResponse = await apiService.getProviderMetrics(provider.id);
                  if (metricsResponse.success) {
                    metrics[provider.id] = metricsResponse.data;
                  }
                } catch (error) {
                  // Silently handle metrics errors
                }
              })
            );
            setProviderMetrics(metrics);
          }
        } catch (error) {
          console.error("Failed to update provider metrics:", error);
        }
      }, 10000); // Update every 10 seconds

      return () => clearInterval(interval);
    }
  }, [realTimeUpdates, wsConnected]);

  const handleSelectProvider = (provider: AIProvider) => {
    setSelectedProvider(provider);
    setIsEditing(false);
  };

  const handleCreateNewProvider = () => {
    const newProvider: AIProvider = {
      id: "",
      name: "New Provider",
      enabled: false,
      apiKey: "",
      baseUrl: "",
      models: [],
      priority: providers.length + 1,
      maxTokens: 4000,
      temperature: 0.7,
      timeout: 30000,
      retryAttempts: 3,
      connected: false,
      latency: 0,
      successRate: 0,
    };

    setSelectedProvider(newProvider);
    setIsEditing(true);
  };

  const handleSaveProvider = async () => {
    if (!selectedProvider) return;

    try {
      setLoading(true);

      let response;
      if (selectedProvider.id) {
        response = await apiService.updateProvider(selectedProvider.id, selectedProvider);
      } else {
        response = await apiService.createProvider(selectedProvider);
      }

      if (response.success && response.data) {
        const updatedProvider = response.data;

        if (selectedProvider.id) {
          setProviders(
            providers.map((provider) =>
              provider.id === selectedProvider.id ? updatedProvider : provider,
            ),
          );
        } else {
          setProviders([...providers, updatedProvider]);
        }

        setSelectedProvider(updatedProvider);
        setIsEditing(false);

        toast({
          title: "Success",
          description: selectedProvider.id
            ? "Provider updated successfully"
            : "Provider created successfully",
        });
      } else {
        throw new Error(response.error || "Failed to save provider");
      }
    } catch (error: any) {
      console.error("Failed to save provider:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save provider",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProvider = async () => {
    if (!selectedProvider || !selectedProvider.id) return;

    try {
      setLoading(true);
      const response = await apiService.deleteProvider(selectedProvider.id);

      if (response.success) {
        setProviders(providers.filter((provider) => provider.id !== selectedProvider.id));
        setSelectedProvider(null);
        toast({
          title: "Success",
          description: "Provider deleted successfully",
        });
      } else {
        throw new Error(response.error || "Failed to delete provider");
      }
    } catch (error: any) {
      console.error("Failed to delete provider:", error);
      toast({
        title: "Error",