import { useState, useEffect, useCallback } from "react";
import { wsService } from "@/services/websocket";
import { stateManager } from "@/services/state";
import { SessionState } from "@/types/api";

export function useRealTimeData<T>(event: string, initialData?: T) {
  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handler = (newData: T) => {
      setData(newData);
      setLoading(false);
      setError(null);
    };

    const errorHandler = (errorData: { error: string }) => {
      setError(errorData.error);
      setLoading(false);
    };

    wsService.on(event, handler);
    wsService.on(`${event}:error`, errorHandler);

    return () => {
      wsService.off(event, handler);
      wsService.off(`${event}:error`, errorHandler);
    };
  }, [event]);

  const sendUpdate = useCallback(
    (updateData: any) => {
      setLoading(true);
      wsService.send(event, updateData);
    },
    [event],
  );

  return { data, loading, error, sendUpdate };
}

export function useSessionState(sessionId: string) {
  const [state, setState] = useState<SessionState | null>(null);

  useEffect(() => {
    // Get initial state
    const initialState = stateManager.getState(sessionId);
    setState(initialState);

    // Subscribe to state changes
    const unsubscribe = stateManager.subscribe(sessionId, (newState) => {
      setState(newState);
    });

    return unsubscribe;
  }, [sessionId]);

  const updateContext = useCallback(
    (context: Record<string, any>) => {
      stateManager.updateContext(sessionId, context);
    },
    [sessionId],
  );

  const updateMemory = useCallback(
    (memory: any[]) => {
      stateManager.updateMemory(sessionId, memory);
    },
    [sessionId],
  );

  const updateVariables = useCallback(
    (variables: Record<string, any>) => {
      stateManager.updateVariables(sessionId, variables);
    },
    [sessionId],
  );

  const addToMemory = useCallback(
    (item: any) => {
      stateManager.addToMemory(sessionId, item);
    },
    [sessionId],
  );

  const setVariable = useCallback(
    (key: string, value: any) => {
      stateManager.setVariable(sessionId, key, value);
    },
    [sessionId],
  );

  return {
    state,
    updateContext,
    updateMemory,
    updateVariables,
    addToMemory,
    setVariable,
  };
}

export function useWebSocketConnection() {
  const [connected, setConnected] = useState(wsService.isConnected());
  const [sessionId] = useState(wsService.getSessionId());

  useEffect(() => {
    const checkConnection = () => {
      setConnected(wsService.isConnected());
    };

    const interval = setInterval(checkConnection, 1000);
    return () => clearInterval(interval);
  }, []);

  return { connected, sessionId };
}
