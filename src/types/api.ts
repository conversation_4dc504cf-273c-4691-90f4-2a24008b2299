import { ReactNode } from "react";
import { z } from "zod";

// Provider Types
export interface AIProvider {
  id: string;
  name: string;
  enabled: boolean;
  apiKey: string;
  baseUrl: string;
  models: string[];
  priority: number;
  maxTokens: number;
  temperature: number;
  timeout: number;
  retryAttempts: number;
  connected: boolean;
  latency: number;
  successRate: number;
}

// Agent Configuration Schema
export const AgentConfigSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Name is required"),
  description: z.string(),
  model: z.string(),
  temperature: z.number().min(0).max(1),
  maxTokens: z.number().min(1).max(32000),
  stateManagement: z.object({
    enabled: z.boolean(),
    persistenceType: z.enum(["redis", "postgres", "memory"]),
    ttl: z.number().min(0),
  }),
  memory: z.object({
    enabled: z.boolean(),
    type: z.enum(["conversational", "summarization", "vector", "hybrid"]),
    contextWindow: z.number().min(1),
  }),
  reasoning: z.object({
    enabled: z.boolean(),
    type: z.enum(["chain-of-thought", "tree-of-thought", "react", "reflexion"]),
    steps: z.number().min(1),
  }),
  hitl: z.object({
    enabled: z.boolean(),
    mode: z.enum(["manual", "auto", "optional"]),
    timeout: z.number().min(0),
    fallbackStrategy: z.enum(["none", "retry", "timeout"]),
    tags: z.array(z.string()),
  }),
  providers: z.array(z.string()),
});

export type AgentConfig = z.infer<typeof AgentConfigSchema>;

// Tool Configuration Schema - Production Implementation
export const ToolConfigSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  inputSchema: z.string().refine((val) => {
    try {
      const parsed = JSON.parse(val);
      return parsed && typeof parsed === "object" && parsed.type === "object";
    } catch {
      return false;
    }
  }, "Must be valid JSON Schema with type 'object'"),
  outputSchema: z.string().refine((val) => {
    try {
      const parsed = JSON.parse(val);
      return parsed && typeof parsed === "object" && parsed.type === "object";
    } catch {
      return false;
    }
  }, "Must be valid JSON Schema with type 'object'"),
  validationRules: z.string().max(1000, "Validation rules too long"),
  endpoint: z.string().url("Must be valid URL").optional(),
  method: z.enum(["GET", "POST", "PUT", "DELETE"]).default("POST"),
  headers: z.record(z.string()).optional(),
  timeout: z.number().min(1000).max(300000).default(30000),
  retryAttempts: z.number().min(0).max(10).default(3),
  authentication: z
    .object({
      type: z
        .enum(["none", "bearer", "basic", "api_key", "oauth2"])
        .default("none"),
      credentials: z.record(z.string()).optional(),
    })
    .optional(),
  rateLimit: z
    .object({
      enabled: z.boolean().default(false),
      requestsPerMinute: z.number().min(1).max(10000).default(60),
      burstLimit: z.number().min(1).max(1000).default(10),
    })
    .optional(),
  caching: z
    .object({
      enabled: z.boolean().default(false),
      ttl: z.number().min(60).max(86400).default(300), // 5 minutes to 24 hours
      keyStrategy: z
        .enum(["input_hash", "custom", "none"])
        .default("input_hash"),
    })
    .optional(),
  hitl: z.object({
    enabled: z.boolean().default(false),
    mode: z.enum(["manual", "auto", "optional"]).default("optional"),
    timeout: z.number().min(30).max(3600).default(300),
    parameterBinding: z.object({
      sessionContext: z.boolean().default(false),
      userContext: z.boolean().default(false),
      agentMemory: z.boolean().default(false),
      customBindings: z.record(z.string()).optional(),
    }),
    fallbackStrategy: z
      .enum([
        "none",
        "retry",
        "timeout",
        "default_value",
        "alternative_endpoint",
      ])
      .default("none"),
    fallbackValue: z.any().optional(),
  }),
  execution: z
    .object({
      isolated: z.boolean().default(true),
      resourceLimits: z.object({
        maxMemoryMB: z.number().min(16).max(1024).default(128),
        maxCpuPercent: z.number().min(10).max(100).default(50),
        maxExecutionTimeMs: z.number().min(1000).max(300000).default(30000),
      }),
      environment: z.record(z.string()).optional(),
    })
    .optional(),
  monitoring: z
    .object({
      enabled: z.boolean().default(true),
      trackUsage: z.boolean().default(true),
      trackPerformance: z.boolean().default(true),
      trackErrors: z.boolean().default(true),
      alertThresholds: z
        .object({
          errorRate: z.number().min(0).max(1).default(0.1), // 10%
          latencyMs: z.number().min(100).max(60000).default(5000), // 5 seconds
          failureCount: z.number().min(1).max(1000).default(10),
        })
        .optional(),
    })
    .optional(),
  metadata: z.object({
    version: z
      .string()
      .regex(/^\d+\.\d+\.\d+$/, "Must be semantic version")
      .default("1.0.0"),
    tags: z.array(z.string().max(50)).max(10).optional(),
    category: z
      .enum([
        "general",
        "api",
        "database",
        "file",
        "network",
        "security",
        "ai",
        "analytics",
        "communication",
        "other",
      ])
      .default("general"),
    author: z.string().max(100).optional(),
    documentation: z.string().url().optional(),
    license: z.string().max(50).optional(),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
    lastTestedAt: z.date().optional(),
    testResults: z
      .object({
        passed: z.number().default(0),
        failed: z.number().default(0),
        lastRun: z.date().optional(),
      })
      .optional(),
  }),
});

export type ToolConfig = z.infer<typeof ToolConfigSchema>;

// Tool-Agent Hybrid Configuration Schema - Production Implementation
export const ToolAgentHybridConfigSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  version: z
    .string()
    .regex(/^\d+\.\d+\.\d+$/, "Must be semantic version")
    .default("1.0.0"),

  // Core composition - references to existing modules
  agentConfig: z.object({
    agentId: z.string().optional(), // Reference to existing agent
    inline: AgentConfigSchema.optional(), // Or inline agent configuration
  }),

  toolConfigs: z.array(
    z.object({
      toolId: z.string().optional(), // Reference to existing tool
      inline: ToolConfigSchema.optional(), // Or inline tool configuration
      priority: z.number().min(0).max(100).default(50),
      conditions: z
        .object({
          enabled: z.boolean().default(true),
          triggerRules: z.array(z.string()).optional(), // JSON Logic rules
          contextRequirements: z.record(z.any()).optional(),
          executionOrder: z.number().min(0).default(0),
        })
        .optional(),
    }),
  ),

  // Hybrid-specific configuration
  orchestration: z.object({
    mode: z
      .enum(["sequential", "parallel", "conditional", "dynamic"])
      .default("sequential"),
    maxConcurrentTools: z.number().min(1).max(10).default(3),
    timeoutMs: z.number().min(1000).max(300000).default(60000),
    retryStrategy: z.object({
      enabled: z.boolean().default(true),
      maxAttempts: z.number().min(1).max(5).default(3),
      backoffMultiplier: z.number().min(1).max(10).default(2),
    }),
    fallbackStrategy: z
      .enum(["none", "agent_only", "tools_only", "best_effort"])
      .default("best_effort"),
  }),

  // Advanced reasoning integration
  reasoning: z.object({
    toolSelectionStrategy: z
      .enum(["agent_driven", "rule_based", "ml_optimized", "hybrid"])
      .default("agent_driven"),
    contextPropagation: z.object({
      enabled: z.boolean().default(true),
      includeToolResults: z.boolean().default(true),
      includeAgentMemory: z.boolean().default(true),
      maxContextSize: z.number().min(1000).max(100000).default(10000),
    }),
    adaptiveBehavior: z.object({
      enabled: z.boolean().default(false),
      learningRate: z.number().min(0.01).max(1.0).default(0.1),
      performanceThreshold: z.number().min(0.1).max(1.0).default(0.8),
    }),
  }),

  // State management for hybrid execution
  stateManagement: z.object({
    enabled: z.boolean().default(true),
    persistenceType: z.enum(["redis", "postgres", "memory"]).default("redis"),
    ttl: z.number().min(300).max(86400).default(3600), // 5 minutes to 24 hours
    sharedContext: z.boolean().default(true),
    isolatedExecution: z.boolean().default(false),
  }),

  // Security and access control
  security: z.object({
    accessControl: z.object({
      enabled: z.boolean().default(true),
      requiredPermissions: z.array(z.string()).optional(),
      roleBasedAccess: z.boolean().default(false),
    }),
    dataIsolation: z.object({
      enabled: z.boolean().default(true),
      encryptSensitiveData: z.boolean().default(true),
      auditTrail: z.boolean().default(true),
    }),
    rateLimiting: z.object({
      enabled: z.boolean().default(true),
      requestsPerMinute: z.number().min(1).max(1000).default(60),
      burstLimit: z.number().min(1).max(100).default(10),
    }),
  }),

  // Monitoring and observability
  monitoring: z.object({
    enabled: z.boolean().default(true),
    trackPerformance: z.boolean().default(true),
    trackToolUsage: z.boolean().default(true),
    trackAgentDecisions: z.boolean().default(true),
    alerting: z.object({
      enabled: z.boolean().default(false),
      errorThreshold: z.number().min(0.01).max(1.0).default(0.1),
      latencyThreshold: z.number().min(100).max(60000).default(5000),
      webhookUrl: z.string().url().optional(),
    }),
  }),

  // Deployment configuration
  deployment: z.object({
    environment: z
      .enum(["development", "staging", "production"])
      .default("development"),
    scalingPolicy: z.object({
      enabled: z.boolean().default(false),
      minInstances: z.number().min(1).max(10).default(1),
      maxInstances: z.number().min(1).max(100).default(5),
      targetCpuUtilization: z.number().min(10).max(90).default(70),
    }),
    healthCheck: z.object({
      enabled: z.boolean().default(true),
      intervalMs: z.number().min(5000).max(300000).default(30000),
      timeoutMs: z.number().min(1000).max(30000).default(5000),
      endpoint: z.string().optional(),
    }),
  }),

  // Metadata and lifecycle
  metadata: z.object({
    tags: z.array(z.string().max(50)).max(20).optional(),
    category: z
      .enum([
        "automation",
        "analysis",
        "communication",
        "integration",
        "workflow",
        "other",
      ])
      .default("automation"),
    author: z.string().max(100).optional(),
    documentation: z.string().url().optional(),
    license: z.string().max(50).optional(),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
    lastDeployedAt: z.date().optional(),
    status: z
      .enum(["draft", "active", "deprecated", "archived"])
      .default("draft"),
    usage: z
      .object({
        totalExecutions: z.number().min(0).default(0),
        successfulExecutions: z.number().min(0).default(0),
        averageExecutionTime: z.number().min(0).default(0),
        lastExecutedAt: z.date().optional(),
      })
      .optional(),
  }),
});

export type ToolAgentHybridConfig = z.infer<typeof ToolAgentHybridConfigSchema>;

// Tool Execution Context - Enhanced Production Implementation
export interface ToolExecutionContext {
  id: string;
  toolId: string;
  sessionId: string;
  correlationId?: string;
  parentExecutionId?: string;
  startTime: Date;
  endTime?: Date;
  status:
    | "pending"
    | "running"
    | "completed"
    | "failed"
    | "timeout"
    | "cancelled"
    | "rate_limited";
  input: any;
  processedInput?: any; // After parameter injection
  output?: any;
  error?: string;
  errorCode?: string;
  stackTrace?: string;
  executionContext: {
    isolated: boolean;
    resourceLimits: {
      maxMemoryMB: number;
      maxCpuPercent: number;
      maxExecutionTimeMs: number;
    };
    environment?: Record<string, string>;
    securityContext?: {
      userId?: string;
      permissions: string[];
      ipAddress?: string;
    };
  };
  metadata: {
    executionTime?: number;
    memoryUsage?: number;
    cpuUsage?: number;
    networkCalls?: number;
    retryCount: number;
    fallbackUsed?: boolean;
    cacheHit?: boolean;
    rateLimitHit?: boolean;
    validationErrors?: string[];
    performanceMetrics?: {
      dnsLookupTime?: number;
      tcpConnectTime?: number;
      tlsHandshakeTime?: number;
      firstByteTime?: number;
      downloadTime?: number;
    };
  };
  events: ToolExecutionEvent[];
}

// Tool Analytics - Comprehensive Production Implementation
export interface ToolAnalytics {
  errorRate: number;
  usageCount: number;
  successCount: number;
  failureCount: number;
  averageLatency: number;
  p50Latency: number;
  p95Latency: number;
  p99Latency: number;
  throughput: number;
  toolId: string;
  timeRange: {
    start: Date;
    end: Date;
  };
  usage: {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    cancelledCalls: number;
    timeoutCalls: number;
    rateLimitedCalls: number;
  };
  performance: {
    averageLatency: number;
    medianLatency: number;
    p50Latency: number;
    p90Latency: number;
    p95Latency: number;
    p99Latency: number;
    minLatency: number;
    maxLatency: number;
    throughput: number; // requests per second
    concurrentExecutions: number;
  };
  reliability: {
    errorRate: number;
    timeoutRate: number;
    successRate: number;
    availabilityPercent: number;
    mttr: number; // Mean Time To Recovery in seconds
    mtbf: number; // Mean Time Between Failures in seconds
  };
  resources: {
    averageMemoryUsage: number;
    peakMemoryUsage: number;
    averageCpuUsage: number;
    peakCpuUsage: number;
    networkBytesIn: number;
    networkBytesOut: number;
  };
  errors: {
    byType: Record<string, number>;
    byCode: Record<string, number>;
    topErrors: Array<{
      error: string;
      count: number;
      lastOccurrence: Date;
    }>;
  };
  caching: {
    hitRate: number;
    missRate: number;
    totalHits: number;
    totalMisses: number;
    averageHitLatency: number;
    averageMissLatency: number;
  };
  rateLimit: {
    totalLimited: number;
    limitRate: number;
    averageWaitTime: number;
  };
  hitl: {
    totalRequests: number;
    resolvedRequests: number;
    timeoutRequests: number;
    averageResolutionTime: number;
  };
  trends: {
    usageGrowth: number; // percentage
    performanceTrend: "improving" | "stable" | "degrading";
    errorTrend: "improving" | "stable" | "degrading";
  };
  lastUpdated: Date;
}

// Tool Event Types - Enhanced Production Implementation
export interface ToolEvent {
  id: string;
  type:
    | "tool_call_start"
    | "tool_call_result"
    | "tool_call_error"
    | "tool_call_timeout"
    | "tool_call_cancelled"
    | "tool_call_rate_limited"
    | "tool_validation_failed"
    | "tool_cache_hit"
    | "tool_cache_miss"
    | "tool_fallback_triggered"
    | "tool_retry_attempted"
    | "tool_hitl_requested"
    | "tool_hitl_resolved";
  toolId: string;
  sessionId: string;
  executionId: string;
  correlationId?: string;
  timestamp: Date;
  data: any;
  metadata?: Record<string, any>;
  severity: "info" | "warning" | "error" | "critical";
  source:
    | "engine"
    | "validator"
    | "executor"
    | "cache"
    | "rate_limiter"
    | "hitl";
}

// Tool Execution Event for detailed tracking
export interface ToolExecutionEvent {
  id: string;
  timestamp: Date;
  type: string;
  message: string;
  data?: any;
  duration?: number;
}

// Parameter Injection Context
export interface ParameterInjectionContext {
  sessionContext?: Record<string, any>;
  userContext?: Record<string, any>;
  agentMemory?: any[];
  customBindings?: Record<string, any>;
}

// Tool Test Result
export interface ToolTestResult {
  success: boolean;
  executionTime: number;
  input: any;
  output?: any;
  error?: string;
  validationErrors?: string[];
  performanceMetrics: {
    latency: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  timestamp: Date;
}

// HITL Types - Enhanced Production Implementation
export interface HITLRequest {
  id: string;
  executionId: string;
  agentId?: string;
  toolId?: string;
  sessionId: string;
  type:
    | "user_input"
    | "decision_override"
    | "validation"
    | "approval"
    | "parameter_injection"
    | "error_resolution";
  category:
    | "functional"
    | "security"
    | "compliance"
    | "quality"
    | "business_logic";
  prompt: string;
  context: Record<string, any>;
  originalInput?: any;
  suggestedResponse?: any;
  validationRules?: string[];
  status: "pending" | "resolved" | "timeout" | "cancelled" | "escalated";
  priority: "low" | "medium" | "high" | "urgent" | "critical";
  severity: "info" | "warning" | "error" | "critical";
  createdAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  response?: any;
  timeout: number;
  escalationPath?: string[];
  approvalChain?: Array<{
    userId: string;
    status: "pending" | "approved" | "rejected";
    timestamp?: Date;
    comment?: string;
  }>;
  metadata: {
    source: string;
    reason: string;
    impact: "low" | "medium" | "high" | "critical";
    businessJustification?: string;
    complianceRequirement?: string;
    riskAssessment?: {
      level: "low" | "medium" | "high" | "critical";
      factors: string[];
      mitigation?: string;
    };
  };
  audit: {
    createdBy: string;
    ipAddress?: string;
    userAgent?: string;
    location?: string;
  };
}

// WebSocket Protocol Types
export interface APXMessage {
  id: string;
  type: "request" | "response" | "event" | "error";
  event: string;
  data: any;
  timestamp: number;
  sessionId: string;
}

// State Management Types
export interface SessionState {
  id: string;
  agentId?: string;
  toolId?: string;
  context: Record<string, any>;
  memory: any[];
  variables: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
}

// Provider Response Types
export interface ProviderResponse {
  success: boolean;
  data?: any;
  error?: string;
  latency: number;
  provider: string;
  model: string;
  tokensUsed?: number;
}

// Tool-Agent Hybrid Execution Context
export interface HybridExecutionContext {
  id: string;
  hybridId: string;
  sessionId: string;
  correlationId?: string;
  parentExecutionId?: string;
  startTime: Date;
  endTime?: Date;
  status:
    | "pending"
    | "running"
    | "completed"
    | "failed"
    | "timeout"
    | "cancelled";

  // Agent execution context
  agentExecution?: {
    agentId: string;
    reasoning: {
      steps: Array<{
        step: number;
        thought: string;
        action?: string;
        observation?: string;
        timestamp: Date;
      }>;
      finalDecision: string;
      confidence: number;
    };
    toolsSelected: string[];
    executionPlan: {
      sequence: Array<{
        toolId: string;
        priority: number;
        conditions?: Record<string, any>;
        expectedOutput?: any;
      }>;
      parallelGroups?: string[][];
    };
  };

  // Tool execution contexts
  toolExecutions: Array<{
    toolId: string;
    executionId: string;
    status: "pending" | "running" | "completed" | "failed" | "skipped";
    startTime: Date;
    endTime?: Date;
    input: any;
    output?: any;
    error?: string;
    metadata: {
      executionTime?: number;
      retryCount: number;
      triggeredBy: "agent" | "rule" | "condition";
      priority: number;
    };
  }>;

  // Hybrid-specific metadata
  hybridMetadata: {
    orchestrationMode: "sequential" | "parallel" | "conditional" | "dynamic";
    totalToolsExecuted: number;
    successfulTools: number;
    failedTools: number;
    skippedTools: number;
    adaptiveBehaviorTriggered: boolean;
    fallbackUsed: boolean;
    contextPropagationSize: number;
    performanceMetrics: {
      totalLatency: number;
      agentReasoningTime: number;
      toolExecutionTime: number;
      contextPropagationTime: number;
      memoryUsage: number;
      cpuUsage: number;
    };
  };

  // Final results
  results: {
    success: boolean;
    output: any;
    confidence: number;
    reasoning: string;
    toolResults: Record<string, any>;
    agentInsights?: string;
    recommendations?: string[];
  };

  // Error handling
  error?: string;
  errorCode?: string;
  stackTrace?: string;

  // Events and audit trail
  events: Array<{
    id: string;
    timestamp: Date;
    type: string;
    source: "agent" | "tool" | "orchestrator" | "system";
    message: string;
    data?: any;
    severity: "info" | "warning" | "error" | "critical";
  }>;
}

// Hybrid Analytics
export interface HybridAnalytics {
  hybridId: string;
  timeRange: {
    start: Date;
    end: Date;
  };

  // Execution statistics
  execution: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    medianExecutionTime: number;
    p95ExecutionTime: number;
    throughput: number; // executions per hour
  };

  // Agent performance
  agentPerformance: {
    averageReasoningTime: number;
    toolSelectionAccuracy: number;
    decisionConfidence: number;
    adaptiveBehaviorTriggers: number;
    memoryUtilization: number;
  };

  // Tool utilization
  toolUtilization: {
    mostUsedTools: Array<{
      toolId: string;
      usageCount: number;
      successRate: number;
      averageExecutionTime: number;
    }>;
    toolSuccessRates: Record<string, number>;
    parallelExecutionEfficiency: number;
  };

  // Resource consumption
  resources: {
    averageMemoryUsage: number;
    peakMemoryUsage: number;
    averageCpuUsage: number;
    peakCpuUsage: number;
    networkBandwidth: number;
    storageUtilization: number;
  };

  // Error analysis
  errors: {
    byCategory: Record<string, number>;
    byComponent: Record<"agent" | "tool" | "orchestrator", number>;
    topErrors: Array<{
      error: string;
      count: number;
      lastOccurrence: Date;
      impact: "low" | "medium" | "high" | "critical";
    }>;
    recoveryRate: number;
  };

  // Performance trends
  trends: {
    executionTimetrend: "improving" | "stable" | "degrading";
    successRateTrend: "improving" | "stable" | "degrading";
    resourceUsageTrend: "improving" | "stable" | "degrading";
    userSatisfactionTrend: "improving" | "stable" | "degrading";
  };

  lastUpdated: Date;
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
}
